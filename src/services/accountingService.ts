import Decimal from "decimal.js";
import { captureException } from "@sentry/node";
import {
  TransactionDocument,
  DepositCashTransactionDocument,
  WithdrawalCashTransactionDocument,
  ChargeTransactionDocument,
  DividendTransactionDocument,
  SavingsDividendTransactionDocument,
  DepositMethodEnum,
  TransactionPopulationFieldsEnum
} from "../models/Transaction";
import { OrderDocument, OrderPopulationFieldsEnum } from "../models/Order";
import { RewardDocument } from "../models/Reward";
import { AccountingRecordIndex } from "../models/AccountingRecordIndex";
import { InvoiceReferenceNumber } from "../models/InvoiceReferenceNumber";
import AccountingLedgerStorageService, {
  AccountingLedgerEntry
} from "../external-services/accountingLedgerStorageService";
import { LedgerAccounts, AccountingClientSegment, AccountingRule, AccountingEventType } from "../types/accounting";
import { clientSegmentToLedgerAccountMapping } from "../configs/accountingConfig";
import { UserDocument } from "../models/User";
import { AccountingEntryGroup } from "../types/accounting";
import DbUtil from "../utils/dbUtil";
import logger from "../external-services/loggerService";

export class AccountingService {
  public static async generateAccountingEntriesOnTransactionUpdate(
    newTransaction: TransactionDocument,
    oldTransaction?: TransactionDocument | null
  ): Promise<void> {
    await DbUtil.populateIfNotAlreadyPopulated(newTransaction, TransactionPopulationFieldsEnum.OWNER);

    // Check if user belongs to European entity
    const user = newTransaction.owner as UserDocument;
    if (!user.isEuropeanEntity) {
      // Skip accounting for non-European entities
      return;
    }

    const userId = user._id.toString();
    const clientSegment = user.accountingClientSegment;

    let ledgerEntries: AccountingLedgerEntry[] = [];
    let rules: AccountingRule[] = [];
    let description: string = "";
    const postingDate = new Date().toISOString().split("T")[0];
    const sourceDocIdStr = newTransaction._id.toString();

    switch (newTransaction.category) {
      case "DepositCashTransaction": {
        // Ensure linkedCreditTicket is populated for deposit transactions
        const deposit = newTransaction as DepositCashTransactionDocument;
        await DbUtil.populateIfNotAlreadyPopulated(deposit, TransactionPopulationFieldsEnum.CREDIT_TICKET);

        rules = AccountingService._generateDepositRules(
          deposit,
          oldTransaction as DepositCashTransactionDocument,
          clientSegment
        );
        description = AccountingService._getAccountingActivityDescription(
          userId,
          sourceDocIdStr,
          AccountingEventType.BANK_TRANSACTION
        );
        break;
      }

      case "WithdrawalCashTransaction": {
        rules = AccountingService._generateWithdrawalRules(
          newTransaction as WithdrawalCashTransactionDocument,
          oldTransaction as WithdrawalCashTransactionDocument,
          clientSegment
        );
        description = AccountingService._getAccountingActivityDescription(
          userId,
          sourceDocIdStr,
          AccountingEventType.BANK_TRANSACTION
        );
        break;
      }

      case "ChargeTransaction": {
        // TODO: Implement custody charge accounting logic
        logger.info("TODO: Implement charge accounting logic for transaction", {
          module: "AccountingService",
          method: "generateAccountingEntriesOnTransactionUpdate",
          data: { transactionId: newTransaction._id }
        });
        break;
      }
    }

    if (rules.length > 0) {
      const newRecord = await new AccountingRecordIndex({
        linkedDocumentId: newTransaction._id,
        sourceDocumentType: "Transaction"
      }).save();
      ledgerEntries = AccountingService._transformRulesToLedgerEntries(rules, {
        aa: newRecord.aaIndex,
        description,
        postingDate
      });
      await AccountingLedgerStorageService.Instance.addValidatedLedgerEntries(ledgerEntries);
    }
  }

  public static async generateAccountingEntriesOnTransactionInsert(
    transaction: TransactionDocument
  ): Promise<void> {
    await DbUtil.populateIfNotAlreadyPopulated(transaction, TransactionPopulationFieldsEnum.OWNER);

    // Check if user belongs to European entity
    const user = transaction.owner as UserDocument;
    if (!user.isEuropeanEntity) {
      // Skip accounting for non-European entities
      return;
    }

    let ledgerEntries: AccountingLedgerEntry[] = [];
    const sourceDocIdStr = transaction._id.toString();
    const postingDate = new Date().toISOString().split("T")[0]; // Or transaction.createdAt
    const userId = user._id.toString();
    const clientSegment = user.accountingClientSegment;
    let accountingElementData: AccountingEntryGroup;

    switch (transaction.category) {
      case "DividendTransaction": {
        const dividendTransaction = transaction as DividendTransactionDocument;
        const wkStatus = dividendTransaction.providers.wealthkernel.status;

        if (dividendTransaction.status === "Settled" || wkStatus === "Matched") {
          const rules = AccountingService._generateStockDividendReceiptRules(dividendTransaction, clientSegment);
          if (rules.length > 0) {
            const eventType = AccountingEventType.ASSET_DIVIDEND;
            const description = AccountingService._getAccountingActivityDescription(
              userId,
              sourceDocIdStr,
              eventType
            );
            accountingElementData = {
              description,
              rules
            };
          }
        } else {
          logger.info(
            `DividendTransaction ${transaction._id} inserted with status ${dividendTransaction.status}/${wkStatus}, not processed for accounting on insert.`,
            {
              module: "AccountingService",
              method: "generateAccountingEntriesOnTransactionInsert"
            }
          );
        }
        break;
      }
      case "SavingsDividendTransaction": {
        const savingsDividendTx = transaction as SavingsDividendTransactionDocument;
        const rules = AccountingService._generateMMFDividendRules(savingsDividendTx, clientSegment);
        const eventType = AccountingEventType.ASSET_DIVIDEND;
        const description = AccountingService._getAccountingActivityDescription(userId, sourceDocIdStr, eventType);
        accountingElementData = {
          description,
          rules,
          referenceNumber: undefined // MMF dividends don't have an invoice number
        };
        break;
      }
    }

    if (accountingElementData && accountingElementData.rules.length > 0) {
      const newRecord = await new AccountingRecordIndex({
        linkedDocumentId: transaction._id,
        sourceDocumentType: transaction.category
      }).save();
      const aa = newRecord.aaIndex;

      ledgerEntries = AccountingService._transformRulesToLedgerEntries(accountingElementData.rules, {
        aa,
        description: accountingElementData.description,
        postingDate,
        referenceNumber: accountingElementData.referenceNumber
      });
    }

    if (ledgerEntries.length > 0) {
      try {
        await AccountingLedgerStorageService.Instance.addValidatedLedgerEntries(ledgerEntries);
      } catch (error) {
        captureException(error);
        logger.error(
          `Exception storing ledger entries for inserted transaction ${transaction._id}: ${error}. AccountingRecordIndex documents might have been created.`,
          {
            module: "AccountingService",
            method: "generateAccountingEntriesOnTransactionInsert"
          }
        );
      }
    }
  }
  public static async generateAccountingEntriesOnOrderUpdate(
    newOrder: OrderDocument,
    oldOrder?: OrderDocument | null
  ): Promise<void> {
    if (!newOrder.populated("transaction")) {
      await newOrder.populate(OrderPopulationFieldsEnum.TRANSACTION);
    }

    const transaction = newOrder.transaction as TransactionDocument;
    if (transaction.category === "ChargeTransaction") {
      return;
    }

    await DbUtil.populateIfNotAlreadyPopulated(transaction, TransactionPopulationFieldsEnum.OWNER);

    // Check if user belongs to European entity
    const user = transaction.owner as UserDocument;
    if (!user.isEuropeanEntity) {
      // Skip accounting for non-European entities
      return;
    }

    const wasPreviouslyProcessed = oldOrder?.isMatched;
    if (!wasPreviouslyProcessed && newOrder.isMatched) {
      const userId = user._id.toString();
      const clientSegment = user.accountingClientSegment;
      const rules = AccountingService._generateAssetOrderRules(newOrder, clientSegment);

      if (rules.length > 0) {
        const sourceDocIdStr = newOrder._id.toString();
        const postingDate = new Date().toISOString().split("T")[0];
        const eventType = newOrder.side === "Buy" ? AccountingEventType.ASSET_BUY : AccountingEventType.ASSET_SELL;
        const description = AccountingService._getAccountingActivityDescription(
          userId,
          sourceDocIdStr,
          eventType,
          newOrder.isin
        );

        const [savedRecord, savedInvoice] = await Promise.all([
          new AccountingRecordIndex({
            linkedDocumentId: newOrder._id,
            sourceDocumentType: "Order"
          }).save(),
          new InvoiceReferenceNumber({
            linkedDocumentId: newOrder._id,
            sourceDocumentType: "Order"
          }).save()
        ]);

        const aa = savedRecord.aaIndex;
        const referenceNumber = savedInvoice.invoiceId.toString();

        const accountingElementData: AccountingEntryGroup = {
          description,
          rules,
          referenceNumber
        };

        const ledgerEntries = AccountingService._transformRulesToLedgerEntries(accountingElementData.rules, {
          aa,
          description: accountingElementData.description,
          postingDate,
          referenceNumber: accountingElementData.referenceNumber
        });

        try {
          await AccountingLedgerStorageService.Instance.addValidatedLedgerEntries(ledgerEntries);
        } catch (error) {
          captureException(error);
          logger.error(
            `Exception storing ledger entries for order ${newOrder._id}: ${error}. AccountingRecordIndex might have been created.`,
            {
              module: "AccountingService",
              method: "generateAccountingEntriesOnOrderUpdate"
            }
          );
        }
      }
    }
  }

  public static async generateAccountingEntriesOnRewardUpdate(
    newReward: RewardDocument,
    oldReward?: RewardDocument | null
  ): Promise<void> {
    // TODO: Implement logic for reward updates
    logger.info("TODO: AccountingService.generateAccountingEntriesOnRewardUpdate", {
      module: "AccountingService",
      method: "generateAccountingEntriesOnRewardUpdate",
      data: {
        newRewardId: newReward._id,
        oldRewardId: oldReward?._id
      }
    });
    return Promise.resolve();
  }

  private static _getAccountingActivityDescription(
    userId: string,
    transactionId: string,
    eventType: AccountingEventType,
    isin?: string
  ): string {
    return `${userId}|${transactionId}${isin ? `|${isin}` : ""}|${eventType}`;
  }

  private static _transformRulesToLedgerEntries(
    rules: AccountingRule[],
    context: {
      aa: number;
      description: string;
      postingDate: string;
      referenceNumber?: string;
    }
  ): AccountingLedgerEntry[] {
    const ledgerEntries: AccountingLedgerEntry[] = [];
    for (const rule of rules) {
      ledgerEntries.push({
        aa: context.aa,
        account_code: rule.account,
        side: rule.type,
        amount: rule.amount / 100, // Convert cents to decimal
        reference_number: context.referenceNumber,
        posting_date: context.postingDate,
        description: context.description
      });
    }
    return ledgerEntries;
  }

  private static _generateDepositRules(
    deposit: DepositCashTransactionDocument,
    oldDeposit: DepositCashTransactionDocument,
    clientSegment: AccountingClientSegment
  ): AccountingRule[] {
    const rules: AccountingRule[] = [];
    const amount = Decimal.div(deposit.consideration.amount ?? 0, 100).toNumber();
    const clientLedgerAccount = clientSegmentToLedgerAccountMapping[clientSegment];

    // Handle bank transfer & direct debit deposit methods
    switch (deposit.depositMethod) {
      case DepositMethodEnum.DIRECT_DEBIT_AND_BANK_TRANSFER:
        // TODO: Implement direct debit accounting logic
        break;

      case DepositMethodEnum.BANK_TRANSFER: {
        // Check if instant flow (CreditTicket is Credited)
        const inInstantFlow = deposit.inInstantMoneyFlow;

        if (inInstantFlow) {
          // Instant flow: All 3 stages (includes intermediary 2)
          rules.push(
            ...AccountingService._generateDepositInstantFlowRules(deposit, oldDeposit, clientLedgerAccount, amount)
          );
        } else {
          // Standard flow: Only stages 1 & 3 (skip intermediary 2)
          rules.push(
            ...AccountingService._generateDepositStandardFlowRules(
              deposit,
              oldDeposit,
              clientLedgerAccount,
              amount
            )
          );
        }
        break;
      }
    }

    return rules;
  }

  private static _generateDepositInstantFlowRules(
    deposit: DepositCashTransactionDocument,
    oldDeposit: DepositCashTransactionDocument,
    clientLedgerAccount: LedgerAccounts,
    amount: number
  ): AccountingRule[] {
    const rules: AccountingRule[] = [];

    // Stage 1: Money received in intermediary 1
    if (this._shouldGenerateDepositStage1Rules(deposit, oldDeposit)) {
      rules.push(
        { account: LedgerAccounts.INTERMEDIARY_DEPOSITS_1, amount, type: "debit" },
        { account: clientLedgerAccount, amount, type: "credit" }
      );
    }

    // Stage 2: Money moved to intermediary 2 (ONLY in instant flow)
    if (this._shouldGenerateDepositStage2Rules(deposit, oldDeposit)) {
      rules.push(
        { account: LedgerAccounts.INTERMEDIARY_DEPOSITS_2, amount, type: "debit" },
        { account: LedgerAccounts.INTERMEDIARY_DEPOSITS_1, amount, type: "credit" }
      );
    }

    // Stage 3: Money settled to omnibus from intermediary 2
    if (this._shouldGenerateDepositStage3Rules(deposit, oldDeposit)) {
      rules.push(
        { account: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS, amount, type: "debit" },
        { account: LedgerAccounts.INTERMEDIARY_DEPOSITS_2, amount, type: "credit" }
      );
    }

    return rules;
  }

  private static _generateDepositStandardFlowRules(
    deposit: DepositCashTransactionDocument,
    oldDeposit: DepositCashTransactionDocument,
    clientLedgerAccount: LedgerAccounts,
    amount: number
  ): AccountingRule[] {
    const rules: AccountingRule[] = [];

    // Stage 1: Money received in intermediary 1
    if (this._shouldGenerateDepositStage1Rules(deposit, oldDeposit)) {
      rules.push(
        { account: LedgerAccounts.INTERMEDIARY_DEPOSITS_1, amount, type: "debit" },
        { account: clientLedgerAccount, amount, type: "credit" }
      );
    }

    // Stage 3: Money settled DIRECTLY from intermediary 1 to omnibus (skipping intermediary 2)
    if (this._shouldGenerateDepositStage3Rules(deposit, oldDeposit)) {
      rules.push(
        { account: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS, amount, type: "debit" },
        { account: LedgerAccounts.INTERMEDIARY_DEPOSITS_1, amount, type: "credit" }
      );
    }

    return rules;
  }

  private static _shouldGenerateDepositStage1Rules(
    deposit: DepositCashTransactionDocument,
    oldDeposit: DepositCashTransactionDocument
  ): boolean {
    // Check if acquisition stage just completed
    const hasConfirmedIncoming =
      deposit.transferWithIntermediary?.acquisition?.incomingPayment?.providers?.devengo?.status === "confirmed";
    const hadConfirmedIncoming =
      oldDeposit?.transferWithIntermediary?.acquisition?.incomingPayment?.providers?.devengo?.status ===
      "confirmed";
    return hasConfirmedIncoming && !hadConfirmedIncoming;
  }

  private static _shouldGenerateDepositStage2Rules(
    deposit: DepositCashTransactionDocument,
    oldDeposit: DepositCashTransactionDocument
  ): boolean {
    // Only relevant for instant flow
    // Check if collection stage just completed
    const hasConfirmedOutgoing =
      deposit.transferWithIntermediary?.collection?.outgoingPayment?.providers?.devengo?.status === "confirmed";
    const hadConfirmedOutgoing =
      oldDeposit?.transferWithIntermediary?.collection?.outgoingPayment?.providers?.devengo?.status ===
      "confirmed";
    return hasConfirmedOutgoing && !hadConfirmedOutgoing;
  }

  private static _shouldGenerateDepositStage3Rules(
    deposit: DepositCashTransactionDocument,
    oldDeposit: DepositCashTransactionDocument
  ): boolean {
    // Check if WealthKernel settled the deposit
    const isSettled = deposit.providers?.wealthkernel?.status === "Settled";
    const wasSettled = oldDeposit?.providers?.wealthkernel?.status === "Settled";
    return isSettled && !wasSettled;
  }

  private static _generateWithdrawalRules(
    withdrawalTx: WithdrawalCashTransactionDocument,
    oldWithdrawalTx: WithdrawalCashTransactionDocument,
    clientSegment: AccountingClientSegment
  ): AccountingRule[] {
    const amount = Decimal.div(withdrawalTx.consideration.amount ?? 0, 100).toNumber();
    const clientLedgerAccount = clientSegmentToLedgerAccountMapping[clientSegment];
    let rules: AccountingRule[] = [];

    // Stage 1: Client to Omnibus
    if (
      withdrawalTx.providers?.wealthkernel?.status === "Settled" &&
      oldWithdrawalTx?.providers?.wealthkernel?.status !== "Settled"
    ) {
      rules = [
        { account: clientLedgerAccount, amount, type: "debit" },
        { account: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS, amount, type: "credit" }
      ];
    }

    // Stage 2: Omnibus to Intermediary
    if (
      withdrawalTx.transferWithIntermediary?.collection?.outgoingPayment?.providers?.devengo?.status ===
        "confirmed" &&
      oldWithdrawalTx?.transferWithIntermediary?.collection?.outgoingPayment?.providers?.devengo?.status !==
        "confirmed"
    ) {
      rules = [
        { account: LedgerAccounts.INTERMEDIARY_WITHDRAWALS, amount, type: "debit" },
        { account: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS, amount, type: "credit" }
      ];
    }

    return rules;
  }

  private static _generateChargeAccountingEntries(
    transaction: ChargeTransactionDocument,
    clientSegment: AccountingClientSegment,
    userId: string,
    referenceNumber?: string
  ): AccountingEntryGroup[] {
    const rules: AccountingRule[] = [];
    let eventType: AccountingEventType | undefined;
    const amount = transaction.originalChargeAmount ?? 0;
    const clientLedgerAccount = clientSegmentToLedgerAccountMapping[clientSegment];
    const transactionId = transaction._id.toString();

    if (transaction.chargeType === "custody") {
      eventType = AccountingEventType.CUSTODY_FEE;
      // PRD: May involve selling assets if insufficient cash. This simplified flow assumes cash is available.
      // 1. Charge client
      rules.push({ account: clientLedgerAccount, amount, type: "debit" });
      rules.push({ account: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS, amount, type: "credit" });
      // 2. Recognize income
      rules.push({ account: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS, amount, type: "debit" });
      rules.push({ account: LedgerAccounts.CUSTODY_FEES_WH, amount, type: "credit" });
    } else if (transaction.chargeType === "commission") {
      eventType = AccountingEventType.COMMISSION_FEE; // Could be WH or Broker related
      // If linked to an AssetTransaction, it should share its referenceNumber
      if (transaction.linkedTransaction) {
        // TODO: Logic to fetch/use referenceNumber from parent AssetTransaction if this is a broker commission part
      }
      rules.push({ account: clientLedgerAccount, amount, type: "debit" });
      rules.push({ account: LedgerAccounts.COMMISSION_FEES_WH, amount, type: "credit" }); // Assuming this is WH income for now
    } else if (transaction.chargeType === "dividendCommission" && transaction.linkedTransaction) {
      eventType = AccountingEventType.MMF_DIVIDEND_COMMISSION;
      rules.push({ account: clientLedgerAccount, amount, type: "debit" });
      rules.push({ account: LedgerAccounts.MMF_DIVIDEND_FEES_WH, amount, type: "credit" });
    }
    // TODO: Add other charge types: fx, executionSpread, subscription, realtimeExecution

    if (rules.length > 0 && eventType) {
      return [
        {
          description: AccountingService._getAccountingActivityDescription(userId, transactionId, eventType),
          referenceNumber,
          rules
        }
      ];
    }
    return [];
  }

  private static _generateStockDividendReceiptRules(
    transaction: DividendTransactionDocument,
    clientSegment: AccountingClientSegment
  ): AccountingRule[] {
    const rules: AccountingRule[] = [];
    const amount = transaction.consideration.amount ?? 0;
    if (amount <= 0) return rules; // No amount, no rules

    const clientLedgerAccount = clientSegmentToLedgerAccountMapping[clientSegment];

    rules.push({ account: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS, amount, type: "debit" });
    rules.push({ account: clientLedgerAccount, amount, type: "credit" });

    return rules;
  }

  private static _generateMMFDividendRules(
    transaction: SavingsDividendTransactionDocument,
    clientSegment: AccountingClientSegment
  ): AccountingRule[] {
    const rules: AccountingRule[] = [];
    const grossAmount = transaction.originalDividendAmount ?? 0;
    const commissionAmount = transaction.fees?.commission?.amount ?? 0;

    if (grossAmount <= 0) {
      return []; // No dividend, no accounting
    }

    const clientLedgerAccount = clientSegmentToLedgerAccountMapping[clientSegment];

    // Rule 1: Gross Dividend Receipt
    rules.push({ account: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS, amount: grossAmount, type: "debit" });
    rules.push({ account: clientLedgerAccount, amount: grossAmount, type: "credit" });

    // Rule 2: Commission Fee on Dividend
    if (commissionAmount > 0) {
      rules.push({ account: clientLedgerAccount, amount: commissionAmount, type: "debit" });
      rules.push({ account: LedgerAccounts.MMF_DIVIDEND_FEES_WH, amount: commissionAmount, type: "credit" });
    }

    // Note: The reinvestment (buy) part is explicitly deferred to a later step
    return rules;
  }

  private static _generateAssetOrderRules(
    order: OrderDocument,
    clientSegment: AccountingClientSegment
  ): AccountingRule[] {
    const rules: AccountingRule[] = [];
    const clientLedgerAccount = clientSegmentToLedgerAccountMapping[clientSegment];

    // Settlement amounts is in cents
    const settlementAmount = Decimal.div(order.consideration.amount ?? 0, 100).toNumber();
    const whCommission = (order.fees?.commission?.amount ?? 0) * 100;
    // FIXME: this will be fixed in a different PR
    const brokerFee = (order.fees?.executionSpread?.amount ?? 0) * 100;
    const netAmount = Decimal.sub(settlementAmount, brokerFee).toNumber();

    // Main Asset Movement
    if (order.side === "Buy") {
      rules.push({ account: clientLedgerAccount, amount: netAmount, type: "debit" });
      rules.push({ account: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS, amount: netAmount, type: "credit" });
      rules.push({ account: LedgerAccounts.ASSETS_ACTIVE, amount: netAmount, type: "debit" });
      rules.push({ account: LedgerAccounts.ASSETS_PASSIVE, amount: netAmount, type: "credit" });
    } else if (order.side === "Sell") {
      rules.push({ account: LedgerAccounts.ASSETS_PASSIVE, amount: netAmount, type: "debit" });
      rules.push({ account: LedgerAccounts.ASSETS_ACTIVE, amount: netAmount, type: "credit" });
      rules.push({ account: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS, amount: netAmount, type: "debit" });
      rules.push({ account: clientLedgerAccount, amount: netAmount, type: "credit" });
    }

    // Wealthyhood Commission
    if (whCommission > 0) {
      rules.push({ account: clientLedgerAccount, amount: whCommission, type: "debit" });
      rules.push({ account: LedgerAccounts.COMMISSION_FEES_WH, amount: whCommission, type: "credit" });
    }

    // Broker Fee (Execution Spread)
    if (brokerFee > 0) {
      rules.push({ account: LedgerAccounts.BROKER_FEE_EXPENSE, amount: brokerFee, type: "debit" });
      rules.push({ account: LedgerAccounts.PAYABLES_TO_BROKER, amount: brokerFee, type: "credit" });
      rules.push({ account: LedgerAccounts.PAYABLES_TO_BROKER, amount: brokerFee, type: "debit" });
      rules.push({ account: LedgerAccounts.CLIENTS_ACCOUNTS_OMNIBUS, amount: brokerFee, type: "credit" });
    }

    return rules;
  }
}
