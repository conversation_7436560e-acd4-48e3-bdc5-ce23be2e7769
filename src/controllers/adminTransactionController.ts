import { CustomRequest } from "custom";
import { Response } from "express";
import mongoose from "mongoose";
import ParamsValidationUtil from "../utils/paramsValidationUtil";
import { TransactionService } from "../services/transactionService";
import InvestmentProductService from "../services/investmentProductService";
import { PaginatedTransactionsResponse } from "apiResponse";
import {
  AssetTransactionDocument,
  TransactionDocument,
  TransactionPopulationFieldsEnum
} from "../models/Transaction";
import { UserDocument } from "../models/User";
import DbUtil from "../utils/dbUtil";

export default class AdminTransactionController {
  public static fetchWealthkernelDividends = async (req: CustomRequest, res: Response): Promise<Response> => {
    await TransactionService.createWealthkernelInvestmentDividends();
    return res.sendStatus(204);
  };

  public static readonly getTransaction = async (req: CustomRequest, res: Response): Promise<Response> => {
    const transactionId = req.params.id;
    if (transactionId) {
      ParamsValidationUtil.isObjectIdParamValid("id", transactionId);
    }
    const populateOrders = req.query.populateOrders
      ? ParamsValidationUtil.isBooleanParamValid("populateOrders", req.query.populateOrders as string)
      : false;

    return res
      .status(200)
      .json(
        await TransactionService.getTransaction(
          transactionId,
          { orders: populateOrders },
          { fillClientDisplayFields: true }
        )
      );
  };

  public static readonly getAssetTransaction = async (req: CustomRequest, res: Response): Promise<Response> => {
    const transactionId = req.params.id;
    if (transactionId) {
      ParamsValidationUtil.isObjectIdParamValid("id", transactionId);
    }
    const populateOrders = req.query.populateOrders
      ? ParamsValidationUtil.isBooleanParamValid("populateOrders", req.query.populateOrders as string)
      : false;

    return res.status(200).json(await TransactionService.getAssetTransaction(transactionId, populateOrders));
  };

  public static readonly getDepositCashTransaction = async (
    req: CustomRequest,
    res: Response
  ): Promise<Response> => {
    const transactionId = req.params.id;
    if (transactionId) {
      ParamsValidationUtil.isObjectIdParamValid("id", transactionId);
    }

    return res.status(200).json(await TransactionService.getDepositCashTransaction(transactionId));
  };

  public static readonly getWithdrawalCashTransaction = async (
    req: CustomRequest,
    res: Response
  ): Promise<Response> => {
    const transactionId = req.params.id;
    if (transactionId) {
      ParamsValidationUtil.isObjectIdParamValid("id", transactionId);
    }

    return res.status(200).json(await TransactionService.getWithdrawalCashTransaction(transactionId));
  };

  public static readonly getTransactions = async (req: CustomRequest, res: Response): Promise<Response> => {
    const filter: { portfolio?: mongoose.Types.ObjectId; owner?: string } = {};
    const portfolio = req.query.portfolio as string;
    if (portfolio) {
      ParamsValidationUtil.isObjectIdParamValid("portfolio", portfolio);
      filter.portfolio = new mongoose.Types.ObjectId(portfolio);
    }
    const owner = req.query.owner;
    if (owner) {
      ParamsValidationUtil.isObjectIdParamValid("owner", owner);
      filter.owner = owner as string;
    }
    const sort = req.query.sort as string;
    const populateOwner = req.query.populateOwner
      ? ParamsValidationUtil.isBooleanParamValid("populateOwner", req.query.populateOwner as string)
      : false;

    const transactions = (await TransactionService.getTransactions(
      filter,
      null,
      { owner: populateOwner },
      sort
    )) as TransactionDocument[];

    const investmentProducts = await InvestmentProductService.getInvestmentProductsDict("isin", true);

    // populate only single order asset transactions to display asset name and side in admin dashboard
    // also calculate display amount for those transactions
    const populatedTransactions = await Promise.all(
      transactions.map(async (transaction) => {
        if (
          transaction.category == "AssetTransaction" &&
          (transaction as AssetTransactionDocument).orders.length == 1 &&
          (transaction as AssetTransactionDocument).portfolioTransactionCategory == "update"
        ) {
          await transaction.populate("orders owner");
          return TransactionService.fillClientDisplayFields(
            transaction.owner as UserDocument,
            transaction,
            investmentProducts
          );
        } else {
          return transaction;
        }
      })
    );
    return res.status(200).json(populatedTransactions);
  };

  public static readonly getAssetTransactionsPaginated = async (
    req: CustomRequest,
    res: Response
  ): Promise<Response> => {
    const pageSize = ParamsValidationUtil.isNumericParamValid("pageSize", req.query.pageSize as string);
    const page = ParamsValidationUtil.isNumericParamValid("page", req.query.page as string);
    const populateOwner =
      ParamsValidationUtil.isBooleanParamValid("populateOwner", req.query.populateOwner as string, {
        isRequired: false
      }) ?? false;
    const populatePendingDeposit = req.query.populatePendingDeposit
      ? ParamsValidationUtil.isBooleanParamValid(
          "populatePendingDeposit",
          req.query.populatePendingDeposit as string
        )
      : false;

    const sort = req.query.sort as string;

    const { transactions, pagination } = (await TransactionService.getAssetTransactions(
      {},
      { page, pageSize },
      { owner: populateOwner, pendingDeposit: populatePendingDeposit },
      sort
    )) as PaginatedTransactionsResponse<AssetTransactionDocument>;

    const investmentProducts = await InvestmentProductService.getInvestmentProductsDict("isin", true);

    // populate only single order asset transactions to display asset name and side in admin dashboard
    // also calculate display amount for those transactions
    const actualRes = {
      transactions: await Promise.all(
        transactions.map(async (transaction) => {
          if (transaction.orders.length == 1 && transaction.portfolioTransactionCategory == "update") {
            await Promise.all([
              DbUtil.populateIfNotAlreadyPopulated(transaction, TransactionPopulationFieldsEnum.ORDERS),
              DbUtil.populateIfNotAlreadyPopulated(transaction, TransactionPopulationFieldsEnum.OWNER)
            ]);

            return TransactionService.fillClientDisplayFields(
              transaction.owner as UserDocument,
              transaction,
              investmentProducts
            );
          } else {
            return transaction;
          }
        })
      ),
      pagination
    };

    return res.status(200).json(actualRes);
  };

  public static readonly getDepositCashTransactionsPaginated = async (
    req: CustomRequest,
    res: Response
  ): Promise<Response> => {
    const pageSize = ParamsValidationUtil.isNumericParamValid("pageSize", req.query.pageSize as string);
    const page = ParamsValidationUtil.isNumericParamValid("page", req.query.page as string);
    const populateOwner =
      ParamsValidationUtil.isBooleanParamValid("populateOwner", req.query.populateOwner as string, {
        isRequired: false
      }) ?? false;
    const sort = req.query.sort as string;

    return res
      .status(200)
      .json(
        await TransactionService.getDepositCashTransactions({}, { page, pageSize }, { owner: populateOwner }, sort)
      );
  };

  public static readonly getWithdrawalCashTransactionsPaginated = async (
    req: CustomRequest,
    res: Response
  ): Promise<Response> => {
    const pageSize = ParamsValidationUtil.isNumericParamValid("pageSize", req.query.pageSize as string);
    const page = ParamsValidationUtil.isNumericParamValid("page", req.query.page as string);
    const populateOwner =
      ParamsValidationUtil.isBooleanParamValid("populateOwner", req.query.populateOwner as string, {
        isRequired: false
      }) ?? false;
    const sort = req.query.sort as string;

    return res.status(200).json(
      await TransactionService.getWithdrawalCashTransactions(
        {},
        { page, pageSize },
        {
          owner: populateOwner
        },
        sort
      )
    );
  };

  public static readonly syncPendingTruelayerDeposits = async (
    req: CustomRequest,
    res: Response
  ): Promise<Response> => {
    await TransactionService.syncPendingTruelayerDeposits();
    return res.sendStatus(204);
  };

  public static readonly processRebalanceTransactions = async (
    req: CustomRequest,
    res: Response
  ): Promise<Response> => {
    await TransactionService.processRebalanceTransactions();
    return res.sendStatus(204);
  };

  public static readonly createMissingWealthkernelWithdrawals = async (
    req: CustomRequest,
    res: Response
  ): Promise<Response> => {
    await TransactionService.createMissingWealthkernelWithdrawals();
    return res.sendStatus(204);
  };

  public static readonly syncPendingWealthkernelWithdrawals = async (
    req: CustomRequest,
    res: Response
  ): Promise<Response> => {
    await TransactionService.syncPendingWealthkernelWithdrawals();
    return res.sendStatus(204);
  };

  public static readonly syncPendingWealthkernelDeposits = async (
    req: CustomRequest,
    res: Response
  ): Promise<Response> => {
    await TransactionService.syncPendingWealthkernelDeposits();
    return res.sendStatus(204);
  };

  public static readonly convertUsersThatHadTransactionsPendingDeposits = async (
    req: CustomRequest,
    res: Response
  ): Promise<Response> => {
    await TransactionService.convertUsersThatHadTransactionsPendingDeposit();
    return res.sendStatus(204);
  };
}
