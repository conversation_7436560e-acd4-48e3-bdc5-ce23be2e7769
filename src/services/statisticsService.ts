import axios from "axios";
import qs from "qs";
import { investmentUniverseConfig } from "@wealthyhood/shared-configs";

const { ASSET_CONFIG } = investmentUniverseConfig;

type PerformanceTenorType = "1m" | "6m" | "1y" | "2y" | "3y" | "5y" | "10y" | "20y" | "30y";

export type PerformanceParamsType = {
  tenor: string;
  initial: number;
  allocation: {
    [key in investmentUniverseConfig.AssetType]: number;
  };
};
export type FuturePerformanceParamsType = {
  monthly: number;
  initial: number;
  allocation: {
    [key in investmentUniverseConfig.AssetType]: number;
  };
};

export class StatisticsService {
  public static readonly URLS = {
    portfolioPastPerformanceV3: `${process.env.STATISTICS_SERVICE_URL}/v3/portfolios/past-performance-all`,
    portfolioFuturePerformanceV3: `${process.env.STATISTICS_SERVICE_URL}/v3/portfolios/future-performance-all`,
    portfolioFuturePerformanceMonteCarloV3: `${process.env.STATISTICS_SERVICE_URL}/v3/portfolios/future-performance-monte-carlo-all`,
    optimalAllocationV3: `${process.env.STATISTICS_SERVICE_URL}/v3/portfolios/optimal-allocation`
  };

  public static async fetchOptimalAllocation(params: { risk: number; asset: string[] }) {
    const response = await axios.get(StatisticsService.URLS.optimalAllocationV3, {
      headers: { "Content-Type": "application/json" },
      params,
      // Even though it looks unused, paramsSerializer is used to correctly pass assets params to the Statistics API
      paramsSerializer: (params) => {
        return qs.stringify(params, { arrayFormat: "repeat" });
      }
    });
    const { allocation } = response.data;
    return allocation;
  }

  public static async fetchPastPerformance(params: PerformanceParamsType) {
    const { tenor, initial, allocation } = params;
    // FIXME: When clients are released, we should make sure they stop using statsUrlMapping and this conversion is updated
    const mappedAllocation = Object.fromEntries(
      Object.entries(allocation).map(([assetId, value]: [investmentUniverseConfig.AssetType, number]) => [
        (ASSET_CONFIG[assetId] as investmentUniverseConfig.ETFAssetConfigType).statsUrlMapping ?? assetId,
        value
      ])
    );

    const response = await axios.get(StatisticsService.URLS.portfolioPastPerformanceV3, {
      headers: { "Content-Type": "application/json" },
      params: {
        tenor,
        initial,
        ...mappedAllocation
      }
    });

    const { total_past_performance_all, metrics } = response.data;
    return { pastPerformance: StatisticsService._roundPerformance(total_past_performance_all), metrics };
  }

  public static async fetchFuturePerformance(params: FuturePerformanceParamsType) {
    const { initial, allocation } = params;
    // FIXME: When clients are released, we should make sure they stop using statsUrlMapping and this conversion is updated
    const mappedAllocation = Object.fromEntries(
      Object.entries(allocation).map(([assetId, value]: [investmentUniverseConfig.AssetType, number]) => [
        (ASSET_CONFIG[assetId] as investmentUniverseConfig.ETFAssetConfigType).statsUrlMapping ?? assetId,
        value
      ])
    );

    const response = await axios.get(StatisticsService.URLS.portfolioFuturePerformanceV3, {
      headers: { "Content-Type": "application/json" },
      params: {
        initial,
        ...mappedAllocation
      }
    });
    const { total_future_performance_all, total_future_performance_all_worse, total_future_performance_all_best } =
      response.data;
    return {
      futurePerformance: StatisticsService._roundPerformance(total_future_performance_all),
      futurePerformanceBest: StatisticsService._roundPerformance(total_future_performance_all_best),
      futurePerformanceWorse: StatisticsService._roundPerformance(total_future_performance_all_worse)
    };
  }

  public static async fetchFuturePerformanceMonteCarlo(params: FuturePerformanceParamsType) {
    const { initial, allocation } = params;
    // FIXME: When clients are released, we should make sure they stop using statsUrlMapping and this conversion is updated
    const mappedAllocation = Object.fromEntries(
      Object.entries(allocation).map(([assetId, value]: [investmentUniverseConfig.AssetType, number]) => [
        (ASSET_CONFIG[assetId] as investmentUniverseConfig.ETFAssetConfigType).statsUrlMapping ?? assetId,
        value
      ])
    );

    const response = await axios.get(StatisticsService.URLS.portfolioFuturePerformanceMonteCarloV3, {
      headers: { "Content-Type": "application/json" },
      params: {
        initial,
        ...mappedAllocation
      }
    });

    const { total_future_performance_all_best, total_future_performance_all_worse } = response.data;
    return {
      futurePerformanceBest: StatisticsService._roundPerformance(total_future_performance_all_best),
      futurePerformanceWorse: StatisticsService._roundPerformance(total_future_performance_all_worse)
    };
  }

  private static _roundPerformance(performance: Record<PerformanceTenorType, Record<string, number>>) {
    return Object.fromEntries(
      Object.entries(performance).map(
        ([tenor, total_past_performance]: [PerformanceTenorType, Record<string, number>]) => {
          return [
            tenor,
            Object.entries(total_past_performance).map(([timestampStr, value]: [string, number]) => ({
              date: new Date(parseInt(timestampStr)),
              value: Math.round(value)
            })) as { date: Date; value: number }[]
          ];
        }
      )
    );
  }
}
