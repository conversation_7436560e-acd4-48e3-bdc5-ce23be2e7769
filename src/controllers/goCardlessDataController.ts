import { Request, Response } from "express";
import { BadRequestError } from "../models/ApiErrors";
import { User } from "../models/User";
import BankAccountService from "../services/bankAccountService";
import RequisitionService from "../services/requisitionService";
import ProviderService, { ProviderScopeEnum } from "../services/providerService";
import { ProviderEnum } from "../configs/providersConfig";
import BanksUtil from "../utils/banksUtil";
import logger from "../external-services/loggerService";

/**
 * Controller for handling GoCardless Data API callbacks.
 */
export default class GoCardlessDataController {
  public static async linkAccount(req: Request, res: Response): Promise<Response> {
    const { userId, reference } = req.body;

    if (!reference) {
      throw new BadRequestError("No reference passed when linking bank account");
    }

    const [requisition, user] = await Promise.all([
      RequisitionService.getRequisitionByReference(reference),
      User.findById(userId)
    ]);

    if (!requisition) {
      throw new BadRequestError(`No requisition found for reference ${reference}`);
    } else if (!user) {
      throw new BadRequestError(`No user found for id '${userId}'`, "Operation failed");
    }

    const goCardlessRequisition = await ProviderService.getOpenBankingDataService(
      user.companyEntity
    ).getRequisition(requisition.providers[ProviderEnum.GOCARDLESS_DATA].id);

    // One requisition could include many accounts, if the user gave us access to more than one account inside
    // their bank app.
    const bankAccounts = await Promise.all(
      goCardlessRequisition.accounts.map(async (accountId) => {
        const [account, details] = await Promise.all([
          ProviderService.getOpenBankingDataService(user.companyEntity).getAccount(accountId),
          ProviderService.getOpenBankingDataService(user.companyEntity).getAccountDetails(accountId)
        ]);

        // GoCardless can also give us card IDs that we do not want to link.
        if (!BanksUtil.isIBAN(account.iban)) {
          logger.info(
            `GoCardless gave us account IBAN ${account.iban} that we cannot link as it does not pass our IBAN validation checks`,
            {
              module: "GoCardlessDataController",
              method: "linkAccount"
            }
          );

          return;
        }

        // GoCardless can also give us non-EUR bank accounts that we do not want to link.
        if (details.currency !== "EUR") {
          logger.info(
            `GoCardless gave us account ${account.iban} that we cannot link as it is not a EUR account`,
            {
              module: "GoCardlessDataController",
              method: "linkAccount"
            }
          );

          return;
        }

        return BankAccountService.createBankAccount({
          iban: account.iban,
          name: account.owner ?? user.fullName, // Sometimes banks do not give us name info through GoCardless
          active: true,
          owner: user.id,
          activeProviders: ProviderService.getProviders(user.companyEntity, [
            ProviderScopeEnum.BANK_ACCOUNTS
          ]).concat([ProviderEnum.GOCARDLESS_DATA]),
          bankId: BanksUtil.getBankFromGoCardlessInstitutionId(account.institution_id),
          providers: {
            gocardlessData: {
              bankId: account.institution_id,
              id: account.id
            }
          }
        });
      })
    );

    return res.status(200).json(bankAccounts);
  }
}
