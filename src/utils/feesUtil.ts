import { FeesType } from "../models/Transaction";
import { currenciesConfig } from "@wealthyhood/shared-configs";
import Decimal from "decimal.js";

export const getZeroFees = (currency: currenciesConfig.MainCurrencyType): FeesType => {
  return {
    fx: {
      currency,
      amount: 0
    },
    commission: {
      currency,
      amount: 0
    },
    executionSpread: {
      currency,
      amount: 0
    },
    realtimeExecution: {
      currency,
      amount: 0
    }
  };
};

export const getTotalFeeAmount = (fees: FeesType): number => {
  if (!fees) return 0;

  return Object.entries(fees)
    .map(([, fee]) => fee.amount)
    .filter((amount) => amount > 0)
    .reduce((sum, fee) => Decimal.add(sum, fee), new Decimal(0))
    .toNumber();
};

export const aggregateFees = (fees: FeesType[], currency: currenciesConfig.MainCurrencyType): FeesType => {
  return fees.reduce((totalFees, currentFees) => {
    return {
      executionSpread: {
        amount: Decimal.add(
          totalFees.executionSpread.amount,
          currentFees?.executionSpread?.amount ?? 0
        ).toNumber(),
        currency: totalFees.executionSpread.currency
      },
      fx: {
        amount: Decimal.add(totalFees.fx.amount, currentFees?.fx?.amount ?? 0).toNumber(),
        currency: totalFees.fx.currency
      },
      commission: {
        amount: Decimal.add(totalFees.commission.amount, currentFees?.commission?.amount ?? 0).toNumber(),
        currency: totalFees.commission.currency
      },
      realtimeExecution: {
        amount: Decimal.add(
          totalFees.realtimeExecution.amount,
          currentFees?.realtimeExecution?.amount ?? 0
        ).toNumber(),
        currency: totalFees.realtimeExecution.currency
      }
    } as FeesType;
  }, getZeroFees(currency));
};
