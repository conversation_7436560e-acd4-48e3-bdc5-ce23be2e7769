import mongoose, { Document, PopulateOptions as MongoosePopulateOptions } from "mongoose";
import { PortfolioPopulationFieldsEnum } from "../models/Portfolio";
import { UserPopulationFieldsEnum } from "../models/User";
import { TransactionPopulationFieldsEnum } from "../models/Transaction";
import { OrderPopulationFieldsEnum } from "../models/Order";
import logger from "../external-services/loggerService";
import { BankAccountPopulationFieldsEnum } from "../models/BankAccount";
import { RewardPopulationFieldsEnum } from "../models/Reward";
import { AccountPopulationFieldsEnum } from "../models/Account";
import { AutomationPopulationFieldsEnum } from "../models/Automation";
import { DailySummarySnapshotPopulationFieldsEnum } from "../models/DailySummarySnapshot";
import { CreditTicketPopulationFieldsEnum } from "../models/CreditTicket";

// Read preference is used to put read load created from cron jobs to the secondary nodes
// and enable the app-api to use the primary one with reduce read load.
export enum MongoReadPreferenceEnum {
  PRIMARY = "primary",
  SECONDARY = "secondary"
}

type PopulationFieldsType =
  | PortfolioPopulationFieldsEnum
  | UserPopulationFieldsEnum
  | TransactionPopulationFieldsEnum
  | BankAccountPopulationFieldsEnum
  | AccountPopulationFieldsEnum
  | RewardPopulationFieldsEnum
  | OrderPopulationFieldsEnum
  | AutomationPopulationFieldsEnum
  | CreditTicketPopulationFieldsEnum
  | DailySummarySnapshotPopulationFieldsEnum;

export type PopulationOptions =
  | string // e.g "owner"
  | string[] // e.g. ["owner", "orders"]
  | {
      path: string;
      populate?: string | PopulationOptions | (string | PopulationOptions)[];
    } // Similar to Mongoose population options
  | {
      path: string;
      populate?: string | PopulationOptions | (string | PopulationOptions)[];
    }[]; // Similar to Mongoose population options

const DEFAULT_NUMBER_OF_RETRIES = 20;

export default class DbUtil {
  /**
   * Method that takes in a function and runs it within the context of a Mongoose session.
   *
   * To retry on transient errors, options.numberOfRetriesForTransientError can be set.
   *
   * @param processorFn
   * @param options
   */
  public static async runInSession<T>(
    processorFn: (session: mongoose.ClientSession) => Promise<T>,
    options: {
      numberOfRetriesForTransientError?: number;
    } = { numberOfRetriesForTransientError: DEFAULT_NUMBER_OF_RETRIES }
  ): Promise<T> {
    const session = await mongoose.startSession({
      defaultTransactionOptions: { readPreference: MongoReadPreferenceEnum.PRIMARY }
    });
    try {
      let retries = 0;
      while (retries < options.numberOfRetriesForTransientError) {
        try {
          session.startTransaction();
          const result: T = await processorFn(session);
          await session.commitTransaction();
          return result;
        } catch (error) {
          try {
            await session.abortTransaction();
          } catch (abortTransactionError) {
            logger.error("We got an error while aborting the transaction!", {
              module: "DbUtil",
              method: "runInSession",
              data: { error, abortTransactionError, retries }
            });
            throw abortTransactionError;
          }

          retries++;
          if (retries >= options.numberOfRetriesForTransientError) {
            throw new Error("Maximum retry limit reached, transaction failed!");
          }

          if (!error.errorLabels || error.errorLabels.indexOf("TransientTransactionError") < 0) {
            throw error;
          }
        }
      }
    } finally {
      await session.endSession();
    }
  }

  /**
   * This method listens to changes in the given model. When a change has been received, we execute the relevant function
   * given. We ONLY listen to change streams if the LISTEN_TO_CHANGE_STREAMS is enabled.
   *
   * **Note**: Since we may have multiple instances listening to change stream events, processor functions **MUST**
   * be idempotent for this to work correctly.
   *
   * @param model
   * @param operationType - The type of operation to listen for ('insert' or 'update')
   * @param onOperationProcessorFn
   */
  public static listenToChangeStream<T>(
    model: mongoose.Model<T>,
    operationType: "insert" | "update",
    onOperationProcessorFn: (document: T) => Promise<void>
  ): void {
    if (process.env.LISTEN_TO_CHANGE_STREAMS === "true") {
      model.watch([], { fullDocument: "updateLookup" }).on("change", async (change) => {
        if (change.operationType === operationType) {
          // plain mongodb document
          const rawDocument = change.fullDocument;
          // hydrated mongoose document
          const document = new model(rawDocument) as T;

          await onOperationProcessorFn(document);
        }
      });
    }
  }

  /**
   * This method extracts the field to be used for sorting along with the ordering type.
   * The ordering type determined from th first char of string param sort '-' for desc, '+' or nothing for asc.
   * @param sort
   */
  public static determineSorting(sort: string): { [key in string]: number } {
    return {
      [sort.replace("+", "").replace("-", "")]: sort.charAt(0) == "-" ? -1 : 1
    };
  }

  public static getMongoosePopulateOption(
    options: PopulationOptions
  ): MongoosePopulateOptions | (MongoosePopulateOptions | string)[] {
    if (typeof options === "string") {
      return { path: options };
    } else return options as MongoosePopulateOptions;
  }

  public static async populateIfNotAlreadyPopulated(
    document: Document,
    path: PopulationFieldsType,
    options?: { session: mongoose.ClientSession }
  ) {
    if (!document.populated(path)) {
      await document.populate({ path, options: { session: options?.session } });
    }
  }

  public static depopulateIfAlreadyPopulated(document: Document, path: PopulationFieldsType) {
    if (document.populated(path)) {
      document.depopulate(path);
    }
  }

  public static getPopulationString(populate: Record<string, boolean>): string {
    if (!populate) {
      return "";
    }

    return Object.entries(populate)
      .filter(([, toPopulate]) => toPopulate)
      .map(([fieldName]) => fieldName)
      .join(" ");
  }
}
