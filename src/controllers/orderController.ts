import { CustomRequest } from "custom";
import { Response } from "express";
import OrderService from "../services/orderService";
import RestUtil from "../utils/restUtil";

export default class OrderController {
  /**
   * Returns a list of pending orders that are polled by the clients to refresh transactions/portfolio.
   */
  /* This endpoint should be deleted after the /latest/matched endpoint has been implemented in the mobile apps */
  public static async getPendingOrders(req: CustomRequest, res: Response): Promise<Response> {
    const owner = req.user.id as string;

    return res.status(200).json({ data: await OrderService.getPendingOrders(owner) });
  }

  public static async getLatestMatchedOrderId(req: CustomRequest, res: Response): Promise<Response> {
    const owner = req.user.id as string;

    const orderId = await OrderService.getLatestMatchedOrderId(owner);

    if (orderId) {
      return res.status(200).json({ id: orderId });
    }

    return res.sendStatus(204);
  }

  public static async cancelOrder(req: CustomRequest, res: Response): Promise<Response> {
    const order = await RestUtil.getOrderFromResponse(req, res, { transaction: true });

    return res.status(200).json(await OrderService.cancelOrder(order));
  }
}
