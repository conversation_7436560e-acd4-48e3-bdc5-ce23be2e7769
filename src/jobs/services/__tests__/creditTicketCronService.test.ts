import { faker } from "@faker-js/faker";
import { clearDb, closeDb, connectDb } from "../../../tests/utils/db";
import { buildCreditTicket, buildPortfolio, buildUser } from "../../../tests/utils/generateModels";
import CreditTicketCronService from "../creditTicketCronService";
import { ProviderEnum } from "../../../configs/providersConfig";
import { CurrencyEnum, WealthkernelService } from "../../../external-services/wealthkernelService";
import { CreditTicket, CreditTicketDocument } from "../../../models/CreditTicket";
import DateUtil from "../../../utils/dateUtil";
import { entitiesConfig } from "@wealthyhood/shared-configs";
import { Portfolio, PortfolioDocument } from "../../../models/Portfolio";

describe("CreditTicketCronService", () => {
  beforeAll(async () => await connectDb("CreditTicketCronService"));
  afterAll(async () => await closeDb());

  describe("createCreditTicketDeposits", () => {
    describe("when there is only a pending credit ticket that is already submitted to WK", () => {
      const NOW = new Date("2024-10-11");

      beforeAll(async () => {
        jest.resetAllMocks();
        Date.now = jest.fn(() => NOW.getTime());

        jest.spyOn(WealthkernelService.EUInstance, "createInternalTransfer");

        const user = await buildUser({
          companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE,
          currency: "EUR"
        });
        const portfolio = await buildPortfolio({
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
        });
        await buildCreditTicket({
          owner: user.id,
          portfolio: portfolio.id,
          consideration: {
            amount: 500,
            currency: "EUR"
          },
          deposit: {
            activeProviders: [ProviderEnum.WEALTHKERNEL],
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Requested",
                submittedAt: NOW
              }
            }
          },
          createdAt: DateUtil.getDateOfDaysAgo(NOW, 1)
        });

        await CreditTicketCronService.createCreditTicketInternalTransfers();
      });
      afterAll(async () => await clearDb());

      it("should not create any WK internal transfers", async () => {
        expect(WealthkernelService.EUInstance.createInternalTransfer).not.toHaveBeenCalled();
      });
    });

    describe("when there is only a credit ticket that is in Credited status", () => {
      const NOW = new Date("2024-10-11");

      beforeAll(async () => {
        jest.resetAllMocks();
        Date.now = jest.fn(() => NOW.getTime());

        jest.spyOn(WealthkernelService.EUInstance, "createInternalTransfer");

        const user = await buildUser({
          companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE,
          currency: "EUR"
        });
        const portfolio = await buildPortfolio({
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
        });
        await buildCreditTicket({
          owner: user.id,
          portfolio: portfolio.id,
          status: "Credited",
          consideration: {
            amount: 500,
            currency: "EUR"
          },
          createdAt: DateUtil.getDateOfDaysAgo(NOW, 1)
        });

        await CreditTicketCronService.createCreditTicketInternalTransfers();
      });
      afterAll(async () => await clearDb());

      it("should not create any WK internal transfers", async () => {
        expect(WealthkernelService.EUInstance.createInternalTransfer).not.toHaveBeenCalled();
      });
    });

    describe("when there is only a credit ticket that does not have Wealthkernel in active providers", () => {
      const NOW = new Date("2024-10-11");

      beforeAll(async () => {
        jest.resetAllMocks();
        Date.now = jest.fn(() => NOW.getTime());

        jest.spyOn(WealthkernelService.EUInstance, "createInternalTransfer");

        const user = await buildUser({
          companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE,
          currency: "EUR"
        });
        const portfolio = await buildPortfolio({
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
        });
        await buildCreditTicket({
          owner: user.id,
          portfolio: portfolio.id,
          deposit: {
            activeProviders: []
          },
          consideration: {
            amount: 500,
            currency: "EUR"
          },
          createdAt: DateUtil.getDateOfDaysAgo(NOW, 1)
        });

        await CreditTicketCronService.createCreditTicketInternalTransfers();
      });
      afterAll(async () => await clearDb());

      it("should not create any WK internal transfers", async () => {
        expect(WealthkernelService.EUInstance.createInternalTransfer).not.toHaveBeenCalled();
      });
    });

    describe("when there is a pending credit ticket and it is above our amount limit", () => {
      const NOW = new Date("2024-10-11");
      const DEPOSIT_ID = faker.string.uuid();

      let creditTicket: CreditTicketDocument;

      beforeAll(async () => {
        jest.resetAllMocks();
        Date.now = jest.fn(() => NOW.getTime());

        jest.spyOn(WealthkernelService.EUInstance, "createInternalTransfer").mockResolvedValue({ id: DEPOSIT_ID });

        const user = await buildUser({
          companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE,
          currency: "EUR"
        });
        const portfolio = await buildPortfolio({
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
        });
        creditTicket = await buildCreditTicket({
          owner: user.id,
          portfolio: portfolio.id,
          status: "Pending",
          consideration: {
            amount: 1000000, // 10.000 EUR
            currency: "EUR"
          },
          createdAt: DateUtil.getDateOfDaysAgo(NOW, 1)
        });

        await CreditTicketCronService.createCreditTicketInternalTransfers();
      });
      afterAll(async () => await clearDb());

      it("should NOT create WK internal transfer", async () => {
        expect(WealthkernelService.EUInstance.createInternalTransfer).not.toHaveBeenCalled();
      });

      it("should update the credit ticket to be Rejected", async () => {
        const updatedCreditTicket = await CreditTicket.findById(creditTicket.id);
        expect(updatedCreditTicket.toObject()).toEqual(
          expect.objectContaining({
            status: "Rejected"
          })
        );
      });
    });

    describe("when there is a pending credit ticket that is unsubmitted but was created less than 15 minutes ago", () => {
      const NOW = new Date("2024-10-11");
      const DEPOSIT_ID = faker.string.uuid();
      const PORTFOLIO_WK_ID = faker.string.uuid();

      beforeAll(async () => {
        jest.resetAllMocks();
        Date.now = jest.fn(() => NOW.getTime());

        jest.spyOn(WealthkernelService.EUInstance, "createInternalTransfer").mockResolvedValue({ id: DEPOSIT_ID });

        const user = await buildUser({
          companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE,
          currency: "EUR"
        });
        const portfolio = await buildPortfolio({
          providers: { wealthkernel: { id: PORTFOLIO_WK_ID, status: "Active" } }
        });
        await buildCreditTicket({
          owner: user.id,
          portfolio: portfolio.id,
          status: "Pending",
          consideration: {
            amount: 500,
            currency: "EUR"
          }
        });

        await CreditTicketCronService.createCreditTicketInternalTransfers();
      });
      afterAll(async () => await clearDb());

      it("should NOT create WK internal transfer", async () => {
        expect(WealthkernelService.EUInstance.createInternalTransfer).not.toHaveBeenCalled();
      });
    });

    describe("when there is a pending credit ticket that is unsubmitted", () => {
      const NOW = new Date("2024-10-11");
      const DEPOSIT_ID = faker.string.uuid();
      const PORTFOLIO_WK_ID = faker.string.uuid();

      let creditTicket: CreditTicketDocument;

      beforeAll(async () => {
        jest.resetAllMocks();
        Date.now = jest.fn(() => NOW.getTime());

        jest.spyOn(WealthkernelService.EUInstance, "createInternalTransfer").mockResolvedValue({ id: DEPOSIT_ID });

        const user = await buildUser({
          companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE,
          currency: "EUR"
        });
        const portfolio = await buildPortfolio({
          providers: { wealthkernel: { id: PORTFOLIO_WK_ID, status: "Active" } }
        });
        creditTicket = await buildCreditTicket({
          owner: user.id,
          portfolio: portfolio.id,
          status: "Pending",
          consideration: {
            amount: 500,
            currency: "EUR"
          },
          createdAt: DateUtil.getDateOfDaysAgo(NOW, 1)
        });

        await CreditTicketCronService.createCreditTicketInternalTransfers();
      });
      afterAll(async () => await clearDb());

      it("should create WK internal transfer", async () => {
        expect(WealthkernelService.EUInstance.createInternalTransfer).toHaveBeenCalledWith(
          {
            fromPortfolioId: process.env.WEALTHKERNEL_WH_PORTFOLIO_ID_EU,
            toPortfolioId: PORTFOLIO_WK_ID,
            consideration: {
              amount: 5,
              currency: "EUR"
            },
            clientReference: creditTicket.id
          },
          creditTicket.id
        );
      });

      it("should add the WK internal transfer information in the credit ticket document", async () => {
        const updatedCreditTicket = await CreditTicket.findById(creditTicket.id);
        expect(updatedCreditTicket.toObject()).toEqual(
          expect.objectContaining({
            status: "Pending",
            deposit: {
              activeProviders: [ProviderEnum.WEALTHKERNEL],
              providers: {
                wealthkernel: {
                  id: DEPOSIT_ID,
                  status: "Requested",
                  submittedAt: NOW
                }
              }
            }
          })
        );
      });
    });
  });

  describe("syncCreditTicketInternalTransfers", () => {
    describe("when there are is only a pending credit ticket that is unsubmitted", () => {
      const NOW = new Date("2024-10-11");
      const PORTFOLIO_WK_ID = faker.string.uuid();

      beforeAll(async () => {
        jest.resetAllMocks();
        Date.now = jest.fn(() => NOW.getTime());

        jest.spyOn(WealthkernelService.EUInstance, "retrieveInternalTransfer");

        const user = await buildUser({
          companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE,
          currency: "EUR"
        });
        const portfolio = await buildPortfolio({
          providers: { wealthkernel: { id: PORTFOLIO_WK_ID, status: "Active" } }
        });
        await buildCreditTicket({
          owner: user.id,
          portfolio: portfolio.id,
          status: "Pending",
          consideration: {
            amount: 500,
            currency: "EUR"
          }
        });

        await CreditTicketCronService.syncCreditTicketInternalTransfers();
      });
      afterAll(async () => await clearDb());

      it("should NOT try to sync WK internal transfer", async () => {
        expect(WealthkernelService.EUInstance.retrieveInternalTransfer).not.toHaveBeenCalled();
      });
    });

    describe("when there are is a pending credit ticket that is already Credited", () => {
      const NOW = new Date("2024-10-11");
      const DEPOSIT_ID = faker.string.uuid();
      const PORTFOLIO_WK_ID = faker.string.uuid();

      beforeAll(async () => {
        jest.resetAllMocks();
        Date.now = jest.fn(() => NOW.getTime());

        jest.spyOn(WealthkernelService.EUInstance, "retrieveInternalTransfer").mockResolvedValue({
          id: DEPOSIT_ID,
          status: "Completed",
          fromPortfolioId: faker.string.uuid(),
          toPortfolioId: PORTFOLIO_WK_ID,
          consideration: { amount: 5, currency: CurrencyEnum.EUR }
        });

        const user = await buildUser({
          companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE,
          currency: "EUR"
        });
        const portfolio = await buildPortfolio({
          providers: { wealthkernel: { id: PORTFOLIO_WK_ID, status: "Active" } }
        });
        await buildCreditTicket({
          owner: user.id,
          portfolio: portfolio.id,
          status: "Credited",
          consideration: {
            amount: 500,
            currency: "EUR"
          },
          deposit: {
            activeProviders: [ProviderEnum.WEALTHKERNEL],
            providers: {
              wealthkernel: {
                id: DEPOSIT_ID,
                status: "Requested",
                submittedAt: DateUtil.getDateOfMinutesAgo(30)
              }
            }
          }
        });

        await CreditTicketCronService.syncCreditTicketInternalTransfers();
      });
      afterAll(async () => await clearDb());

      it("should NOT try to sync WK internal transfer", async () => {
        expect(WealthkernelService.EUInstance.retrieveInternalTransfer).not.toHaveBeenCalled();
      });
    });

    describe("when there are is a pending credit ticket that is submitted less than 15 minutes ago and WK internal transfer is pending", () => {
      const NOW = new Date("2024-10-11");
      const DEPOSIT_ID = faker.string.uuid();
      const PORTFOLIO_WK_ID = faker.string.uuid();

      beforeAll(async () => {
        jest.resetAllMocks();
        Date.now = jest.fn(() => NOW.getTime());

        jest.spyOn(WealthkernelService.EUInstance, "retrieveInternalTransfer").mockResolvedValue({
          id: DEPOSIT_ID,
          status: "Completed",
          fromPortfolioId: faker.string.uuid(),
          toPortfolioId: PORTFOLIO_WK_ID,
          consideration: { amount: 5, currency: CurrencyEnum.EUR }
        });

        const user = await buildUser({
          companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE,
          currency: "EUR"
        });
        const portfolio = await buildPortfolio({
          providers: { wealthkernel: { id: PORTFOLIO_WK_ID, status: "Active" } }
        });
        await buildCreditTicket({
          owner: user.id,
          portfolio: portfolio.id,
          status: "Pending",
          consideration: {
            amount: 500,
            currency: "EUR"
          },
          deposit: {
            activeProviders: [ProviderEnum.WEALTHKERNEL],
            providers: {
              wealthkernel: {
                id: DEPOSIT_ID,
                status: "Requested",
                submittedAt: DateUtil.getDateOfMinutesAgo(10)
              }
            }
          }
        });

        await CreditTicketCronService.syncCreditTicketInternalTransfers();
      });
      afterAll(async () => await clearDb());

      it("should NOT try to sync WK internal transfer", async () => {
        expect(WealthkernelService.EUInstance.retrieveInternalTransfer).not.toHaveBeenCalled();
      });
    });

    describe("when there are is a pending credit ticket that is submitted more than 15 minutes ago and WK internal transfer is pending", () => {
      const NOW = new Date("2024-10-11");
      const DEPOSIT_ID = faker.string.uuid();
      const PORTFOLIO_WK_ID = faker.string.uuid();

      let creditTicket: CreditTicketDocument;
      let portfolio: PortfolioDocument;

      beforeAll(async () => {
        jest.resetAllMocks();
        Date.now = jest.fn(() => NOW.getTime());

        jest.spyOn(WealthkernelService.EUInstance, "retrieveInternalTransfer").mockResolvedValue({
          id: DEPOSIT_ID,
          status: "Completed",
          fromPortfolioId: faker.string.uuid(),
          toPortfolioId: PORTFOLIO_WK_ID,
          consideration: { amount: 5, currency: CurrencyEnum.EUR }
        });

        const user = await buildUser({
          companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE,
          currency: "EUR"
        });
        portfolio = await buildPortfolio({
          providers: { wealthkernel: { id: PORTFOLIO_WK_ID, status: "Active" } }
        });
        creditTicket = await buildCreditTicket({
          owner: user.id,
          portfolio: portfolio.id,
          status: "Pending",
          consideration: {
            amount: 500,
            currency: "EUR"
          },
          deposit: {
            activeProviders: [ProviderEnum.WEALTHKERNEL],
            providers: {
              wealthkernel: {
                id: DEPOSIT_ID,
                status: "Requested",
                submittedAt: DateUtil.getDateOfMinutesAgo(30)
              }
            }
          }
        });

        await CreditTicketCronService.syncCreditTicketInternalTransfers();
      });
      afterAll(async () => await clearDb());

      it("should sync WK internal transfer", async () => {
        expect(WealthkernelService.EUInstance.retrieveInternalTransfer).toHaveBeenCalledWith(DEPOSIT_ID);
      });

      it("should update credit ticket with updated WK data", async () => {
        const updatedCreditTicket = await CreditTicket.findById(creditTicket.id);
        expect(updatedCreditTicket.toObject()).toEqual(
          expect.objectContaining({
            status: "Credited",
            deposit: {
              activeProviders: [ProviderEnum.WEALTHKERNEL],
              providers: {
                wealthkernel: expect.objectContaining({
                  id: DEPOSIT_ID,
                  status: "Completed"
                })
              }
            }
          })
        );
      });

      it("should update the user's cash availability", async () => {
        const updatedPortfolio = await Portfolio.findById(portfolio.id);
        expect(updatedPortfolio.toObject()).toEqual(
          expect.objectContaining({
            cash: expect.objectContaining({
              EUR: expect.objectContaining({
                available: 5,
                reserved: 0,
                settled: 5
              })
            })
          })
        );
      });
    });
  });
});
