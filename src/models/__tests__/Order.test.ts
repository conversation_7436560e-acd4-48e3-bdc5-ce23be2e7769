import { OrderDocument, UnitPriceType, OrderDTOInterface, OrderSubmissionIntentEnum } from "../Order";
import { connectDb, clearDb, closeDb } from "../../tests/utils/db";
import {
  buildOrder,
  buildUser,
  buildAssetTransaction,
  buildInvestmentProduct
} from "../../tests/utils/generateModels";
import { currenciesConfig, investmentUniverseConfig } from "@wealthyhood/shared-configs";
import { ProviderEnum } from "../../configs/providersConfig";
import { InvestmentProductDocument } from "../InvestmentProduct";
import { AssetTransactionDocument } from "../Transaction";

const { ASSET_CONFIG } = investmentUniverseConfig;

describe("Order", () => {
  beforeAll(async () => await connectDb("Order"));
  afterEach(async () => await clearDb());
  afterAll(async () => await closeDb());

  // Helper to create a base OrderDTOInterface with defaults for buildOrder
  const createBaseOrderData = async (overrides: Partial<OrderDTOInterface>): Promise<OrderDTOInterface> => {
    const user = await buildUser({}, false);
    const transaction = await buildAssetTransaction({ owner: user._id });

    const defaults: OrderDTOInterface = {
      side: "Buy",
      status: "Pending",
      consideration: {
        currency: "GBP",
        amount: 10000,
        originalAmount: 10000
      },
      quantity: 10,
      settlementCurrency: "GBP",
      activeProviders: [ProviderEnum.WEALTHKERNEL],
      submissionIntent: OrderSubmissionIntentEnum.AGGREGATE,
      transaction: transaction._id,
      unitPrice: { currency: "GBP", amount: 10 },
      exchangeRate: 1,
      fees: {
        fx: { currency: "GBP", amount: 0 }
      },
      createdAt: new Date(),
      updatedAt: new Date(),
      isin: ASSET_CONFIG["equities_us"].isin
    };

    const finalConsideration = { ...defaults.consideration, ...overrides.consideration };
    const finalFees = { ...defaults.fees, ...overrides.fees };

    const mergedOverrides = {
      ...defaults,
      ...overrides,
      consideration: finalConsideration,
      fees: finalFees
    };

    return mergedOverrides as OrderDTOInterface;
  };

  describe("displayUnitPrice", () => {
    it("should calculate displayUnitPrice correctly for a BUY order with originalAmount, fee, and exchangeRate = 1", async () => {
      const orderData = await createBaseOrderData({
        side: "Buy",
        consideration: {
          originalAmount: 1000, // €10 in cents
          amount: 900,
          currency: "EUR"
        },
        quantity: 1,
        fees: {
          fx: { currency: "EUR", amount: 0 },
          realtimeExecution: {
            amount: 1, // €1
            currency: "EUR"
          }
        },
        exchangeRate: 1,
        unitPrice: { currency: "EUR", amount: 9 },
        settlementCurrency: "EUR"
      });
      const order = await buildOrder(orderData);

      const expectedUnitPrice: UnitPriceType = {
        amount: 9,
        currency: "EUR"
      };
      expect(order.displayUnitPrice).toEqual(expectedUnitPrice);
    });

    it("should calculate displayUnitPrice for BUY order using consideration.amount if originalAmount is missing, with fee", async () => {
      const orderData = await createBaseOrderData({
        side: "Buy",
        consideration: {
          originalAmount: undefined,
          amount: 900, // €9 in cents
          currency: "EUR"
        },
        quantity: 1,
        fees: {
          fx: { currency: "EUR", amount: 0 },
          realtimeExecution: {
            amount: 1, // €1
            currency: "EUR"
          }
        },
        exchangeRate: 1,
        unitPrice: { currency: "EUR", amount: 8 },
        settlementCurrency: "EUR"
      });
      const order = await buildOrder(orderData);

      const expectedUnitPrice: UnitPriceType = {
        amount: 8,
        currency: "EUR"
      };
      expect(order.displayUnitPrice).toEqual(expectedUnitPrice);
    });

    it("should calculate displayUnitPrice for a BUY order with originalAmount, NO fee, and exchangeRate = 1", async () => {
      const orderData = await createBaseOrderData({
        side: "Buy",
        consideration: {
          originalAmount: 1000, // €10 in cents
          amount: 1000,
          currency: "EUR"
        },
        quantity: 1,
        fees: { fx: { currency: "EUR", amount: 0 } /* No realtimeExecution fee */ },
        exchangeRate: 1,
        unitPrice: { currency: "EUR", amount: 10 },
        settlementCurrency: "EUR"
      });
      const order = await buildOrder(orderData);

      const expectedUnitPrice: UnitPriceType = {
        amount: 10,
        currency: "EUR"
      };
      expect(order.displayUnitPrice).toEqual(expectedUnitPrice);
    });

    it("should calculate displayUnitPrice correctly for a SELL order with amount, fee, and exchangeRate = 1", async () => {
      const orderData = await createBaseOrderData({
        side: "Sell",
        consideration: {
          amount: 1000, // €10 in cents
          currency: "EUR"
        },
        quantity: 1,
        fees: {
          fx: { currency: "EUR", amount: 0 },
          realtimeExecution: {
            amount: 1, // €1
            currency: "EUR"
          }
        },
        exchangeRate: 1,
        unitPrice: { currency: "EUR", amount: 11 },
        settlementCurrency: "EUR"
      });
      const order = await buildOrder(orderData);

      const expectedUnitPrice: UnitPriceType = {
        amount: 11,
        currency: "EUR"
      };
      expect(order.displayUnitPrice).toEqual(expectedUnitPrice);
    });

    it("should calculate displayUnitPrice for a SELL order with amount, NO fee, and exchangeRate = 1", async () => {
      const orderData = await createBaseOrderData({
        side: "Sell",
        consideration: {
          amount: 1000, // €10 in cents
          currency: "EUR"
        },
        quantity: 1,
        fees: { fx: { currency: "EUR", amount: 0 } /* No realtimeExecution fee */ },
        exchangeRate: 1,
        unitPrice: { currency: "EUR", amount: 10 },
        settlementCurrency: "EUR"
      });
      const order = await buildOrder(orderData);

      const expectedUnitPrice: UnitPriceType = {
        amount: 10,
        currency: "EUR"
      };
      expect(order.displayUnitPrice).toEqual(expectedUnitPrice);
    });

    it("should calculate displayUnitPrice for BUY order with a different exchangeRate", async () => {
      const orderData = await createBaseOrderData({
        side: "Buy",
        consideration: {
          originalAmount: 8000, // £80 in cents
          currency: "GBP"
        },
        quantity: 10,
        fees: {
          fx: { currency: "GBP", amount: 0 },
          realtimeExecution: {
            amount: 0.8, // £0.80
            currency: "GBP"
          }
        },
        exchangeRate: 1.25, // GBP_TO_USD rate
        unitPrice: { currency: "USD", amount: 9.9 },
        settlementCurrency: "USD"
      });
      const order: OrderDocument = await buildOrder(orderData);

      const expectedUnitPrice: UnitPriceType = {
        amount: 9.9,
        currency: "USD"
      };
      expect(order.displayUnitPrice).toEqual(expectedUnitPrice);
    });

    it("should return underlying unitPrice if consideration.amount is missing (and originalAmount for Buy)", async () => {
      const fallbackUnitPrice = { amount: 12.34, currency: "EUR" as currenciesConfig.MainCurrencyType };
      const orderData = await createBaseOrderData({
        side: "Buy",
        consideration: {
          originalAmount: undefined,
          amount: undefined,
          currency: "EUR"
        },
        quantity: 1,
        unitPrice: fallbackUnitPrice,
        settlementCurrency: "EUR"
      });
      const order = await buildOrder(orderData);
      expect(order.displayUnitPrice).toEqual(fallbackUnitPrice);
    });

    it("should return underlying unitPrice if quantity is missing or zero", async () => {
      const fallbackUnitPrice = { amount: 15.67, currency: "EUR" as currenciesConfig.MainCurrencyType };
      let orderData = await createBaseOrderData({
        side: "Buy",
        consideration: {
          originalAmount: 1000,
          amount: 1000,
          currency: "EUR"
        },
        quantity: undefined,
        unitPrice: fallbackUnitPrice,
        settlementCurrency: "EUR"
      });
      let order = await buildOrder(orderData);
      expect(order.displayUnitPrice).toEqual(fallbackUnitPrice);

      orderData = await createBaseOrderData({
        side: "Buy",
        consideration: {
          originalAmount: 1000,
          amount: 1000,
          currency: "EUR"
        },
        quantity: 0,
        unitPrice: fallbackUnitPrice,
        settlementCurrency: "EUR"
      });
      order = await buildOrder(orderData);
      expect(order.displayUnitPrice).toEqual(fallbackUnitPrice);
    });

    it("should use FALLBACK_EXCHANGE_RATE (1) if order.exchangeRate is missing", async () => {
      const orderData = await createBaseOrderData({
        side: "Buy",
        consideration: {
          originalAmount: 1000,
          amount: 1000,
          currency: "EUR"
        },
        quantity: 1,
        fees: { fx: { currency: "EUR", amount: 0 } },
        exchangeRate: undefined,
        unitPrice: { currency: "EUR", amount: 10 },
        settlementCurrency: "EUR"
      });
      const order = await buildOrder(orderData);

      const expectedUnitPrice: UnitPriceType = {
        amount: 10,
        currency: "EUR"
      };
      expect(order.displayUnitPrice).toEqual(expectedUnitPrice);
    });
  });

  describe("getDisplayAmount", () => {
    let mockInvestmentProduct: InvestmentProductDocument;
    let mockTransaction: AssetTransactionDocument;

    beforeEach(async () => {
      // Create a generic investment product for tests that need it
      mockInvestmentProduct = await buildInvestmentProduct(true, {
        assetId: "equities_us", // or any relevant assetId
        price: 10, // 10 units of currency
        currentTicker: {
          // Ensure currentTicker is populated
          getPrice: (currency: currenciesConfig.MainCurrencyType) => {
            if (currency === "EUR") return 10; // €10
            if (currency === "GBP") return 8.5; // £8.50
            return 10; // Default
          }
        }
      });
      // Create a generic transaction
      const user = await buildUser({}, false);
      mockTransaction = await buildAssetTransaction({ owner: user._id, status: "Pending" });
    });

    describe("for SELL orders", () => {
      it("should return undefined if transaction status is Rejected", async () => {
        mockTransaction.status = "Rejected";
        await mockTransaction.save();
        const orderData = await createBaseOrderData({ side: "Sell", transaction: mockTransaction._id });
        const order = await buildOrder(orderData);
        const displayAmount = order.getDisplayAmount("EUR", mockInvestmentProduct, mockTransaction);
        expect(displayAmount).toBeUndefined();
      });

      it("should return undefined if transaction status is Cancelled", async () => {
        mockTransaction.status = "Cancelled";
        await mockTransaction.save();
        const orderData = await createBaseOrderData({ side: "Sell", transaction: mockTransaction._id });
        const order = await buildOrder(orderData);
        const displayAmount = order.getDisplayAmount("EUR", mockInvestmentProduct, mockTransaction);
        expect(displayAmount).toBeUndefined();
      });

      it("should return undefined if transaction status is DepositFailed", async () => {
        mockTransaction.status = "DepositFailed";
        await mockTransaction.save();
        const orderData = await createBaseOrderData({ side: "Sell", transaction: mockTransaction._id });
        const order = await buildOrder(orderData);
        const displayAmount = order.getDisplayAmount("EUR", mockInvestmentProduct, mockTransaction);
        expect(displayAmount).toBeUndefined();
      });

      it("should calculate estimated amount if consideration.amount is missing (pending sell)", async () => {
        const orderData = await createBaseOrderData({
          side: "Sell",
          consideration: { amount: undefined, currency: "EUR" }, // No consideration.amount
          quantity: 5,
          transaction: mockTransaction._id // mockTransaction status is "Pending" by default here
        });
        const order = await buildOrder(orderData);
        // Expected: investmentProduct.currentTicker.getPrice("EUR") * quantity * 100
        // 10 (price) * 5 (quantity) * 100 = 5000 cents
        const displayAmount = order.getDisplayAmount("EUR", mockInvestmentProduct, mockTransaction);
        expect(displayAmount).toBe(5000);
      });

      it("should return consideration.amount if present (settled sell)", async () => {
        const orderData = await createBaseOrderData({
          side: "Sell",
          consideration: { amount: 12345, currency: "EUR" }, // 123.45 EUR in cents
          quantity: 5,
          transaction: mockTransaction._id
        });
        const order = await buildOrder(orderData);
        const displayAmount = order.getDisplayAmount("EUR", mockInvestmentProduct, mockTransaction);
        expect(displayAmount).toBe(12345);
      });
    });

    describe("for BUY orders", () => {
      it("should return originalAmount if present", async () => {
        const orderData = await createBaseOrderData({
          side: "Buy",
          consideration: { originalAmount: 5000, amountSubmitted: 4900, amount: 4800, currency: "GBP" },
          transaction: mockTransaction._id
        });
        const order = await buildOrder(orderData);
        const displayAmount = order.getDisplayAmount("GBP", mockInvestmentProduct, mockTransaction);
        expect(displayAmount).toBe(5000);
      });

      it("should return amountSubmitted if originalAmount is missing and amountSubmitted is present", async () => {
        const orderData = await createBaseOrderData({
          side: "Buy",
          consideration: { originalAmount: undefined, amountSubmitted: 4900, amount: 4800, currency: "GBP" },
          transaction: mockTransaction._id
        });
        const order = await buildOrder(orderData);
        const displayAmount = order.getDisplayAmount("GBP", mockInvestmentProduct, mockTransaction);
        expect(displayAmount).toBe(4900);
      });

      it("should return amount if originalAmount and amountSubmitted are missing", async () => {
        const orderData = await createBaseOrderData({
          side: "Buy",
          consideration: { originalAmount: undefined, amountSubmitted: undefined, amount: 4800, currency: "GBP" },
          transaction: mockTransaction._id
        });
        const order = await buildOrder(orderData);
        const displayAmount = order.getDisplayAmount("GBP", mockInvestmentProduct, mockTransaction);
        expect(displayAmount).toBe(4800);
      });
    });
  });
});
