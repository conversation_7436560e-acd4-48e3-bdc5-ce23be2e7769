import { CreditTicket, CreditTicketDocument, CreditTicketPopulationFieldsEnum } from "../models/CreditTicket";
import DbUtil from "../utils/dbUtil";
import { UserDocument } from "../models/User";
import ProviderService from "./providerService";
import logger from "../external-services/loggerService";
import { captureException } from "@sentry/node";
import { InternalTransferStatusType, WealthkernelService } from "../external-services/wealthkernelService";
import mongoose from "mongoose";
import PortfolioService from "./portfolioService";
import { PortfolioDocument } from "../models/Portfolio";
import Decimal from "decimal.js";
import { DepositCashTransactionDocument } from "../models/Transaction";
import { CreditTicketRepository } from "../repositories/creditTicketRepository";
import { MainCurrencyToWealthkernelCurrency } from "../configs/currenciesConfig";

const CREDIT_TICKET_AMOUNT_LIMIT = 1000; // 10 EUR

export default class CreditTicketService {
  /**
   * PUBLIC METHODS
   */

  /**
   * @description Creates a CreditTicket for a deposit if one doesn't already exist.
   *
   * @param deposit The deposit to create a CreditTicket for
   * @param options
   */
  public static async createCreditTicketForDeposit(
    deposit: DepositCashTransactionDocument,
    options: { session?: mongoose.ClientSession; requestInstantly?: boolean } = {}
  ): Promise<void> {
    if (deposit.linkedCreditTicket) {
      return;
    }

    const creditTicket = await new CreditTicket({
      owner: deposit.owner,
      portfolio: deposit.portfolio,
      consideration: {
        currency: deposit.consideration.currency,
        amount: deposit.consideration.amount
      },
      status: "Pending",
      deposit: {
        activeProviders: deposit.activeProviders
      }
    }).save({ session: options?.session });

    await deposit.updateOne({ linkedCreditTicket: creditTicket.id }, { session: options?.session });

    logger.info(`Created credit ticket for deposit ${deposit.id}`, {
      module: "CreditTicketService",
      method: "createCreditTicketForDeposit",
      data: { depositId: deposit.id, creditTicketId: creditTicket.id }
    });

    if (options?.requestInstantly) {
      await CreditTicketService.createInternalTransferForCreditTicketSafely(creditTicket, {
        session: options?.session
      });
    }
  }

  /**
   * @description This method is used for bulk operations triggered usually from cron jobs.
   * It adds error handling in order to just report the error but not stop the execution of bulk operation.
   * @param creditTicket
   */
  public static async syncCreditTicketWithPendingInternalTransferSafely(
    creditTicket: CreditTicketDocument
  ): Promise<void> {
    await DbUtil.populateIfNotAlreadyPopulated(creditTicket, CreditTicketPopulationFieldsEnum.OWNER);
    const user = creditTicket.owner as UserDocument;

    try {
      const wkDeposit = await ProviderService.getBrokerageService(user.companyEntity).retrieveInternalTransfer(
        creditTicket.deposit.providers.wealthkernel.id
      );

      await CreditTicketService.updateCreditTicketWealthkernelStatus(creditTicket, wkDeposit.status);
    } catch (err) {
      captureException(err);
      logger.error(`Syncing with wealthkernel failed for deposit ${creditTicket.id}`, {
        module: "CreditTicketService",
        method: "syncCreditTicketWithPendingInternalTransferSafely",
        data: { creditTicketId: creditTicket.id, userId: user.id, error: err }
      });
    }
  }

  public static async createInternalTransferForCreditTicketSafely(
    creditTicket: CreditTicketDocument,
    options: { session?: mongoose.ClientSession } = {}
  ): Promise<void> {
    try {
      await DbUtil.populateIfNotAlreadyPopulated(creditTicket, CreditTicketPopulationFieldsEnum.PORTFOLIO);
      const portfolio = creditTicket.portfolio as PortfolioDocument;

      if (creditTicket.consideration.amount > CREDIT_TICKET_AMOUNT_LIMIT) {
        await CreditTicketRepository.rejectCreditTicket(creditTicket.id, { session: options?.session });

        return;
      }

      // Create internal transfer from Wealthyhood portfolio to user's portfolio
      const internalTransfer = await WealthkernelService.EUInstance.createInternalTransfer(
        {
          fromPortfolioId: process.env.WEALTHKERNEL_WH_PORTFOLIO_ID_EU,
          toPortfolioId: portfolio.providers.wealthkernel.id,
          consideration: {
            currency: MainCurrencyToWealthkernelCurrency[creditTicket.consideration.currency],
            amount: Decimal.div(creditTicket.consideration.amount, 100).toNumber()
          },
          clientReference: creditTicket.id
        },
        creditTicket.id
      );

      logger.info(
        `Created credit ticket internal transfer for credit ticket ${creditTicket.id} with WK id ${internalTransfer.id}`,
        {
          module: "CreditTicketCronService",
          method: "createCreditTicketDeposits"
        }
      );

      await CreditTicket.findByIdAndUpdate(
        creditTicket.id,
        {
          "deposit.providers.wealthkernel": {
            status: "Requested",
            id: internalTransfer.id,
            submittedAt: new Date(Date.now())
          }
        },
        { session: options?.session }
      );
    } catch (err) {
      captureException(err);
      logger.error(`Failed to create credit ticket internal transfer ${creditTicket.id}`, {
        module: "CreditTicketCronService",
        method: "createCreditTicketDeposits",
        data: {
          error: err
        }
      });
    }
  }

  public static async updateCreditTicketWealthkernelStatus(
    creditTicket: CreditTicketDocument,
    newWkStatus: InternalTransferStatusType
  ): Promise<void> {
    if (
      creditTicket?.deposit?.providers?.wealthkernel?.status === "Completed" ||
      creditTicket.status !== "Pending"
    ) {
      logger.warn(`Cannot update WK deposit for credit ticket ${creditTicket.id} as it is not pending!`, {
        module: "CreditTicketService",
        method: "updateCreditTicketWealthkernelStatus"
      });
      return;
    }

    logger.info(`Syncing credit ticket ${creditTicket.id}`, {
      module: "CreditTicketService",
      method: "updateCreditTicketWealthkernelStatus"
    });

    if (newWkStatus === "Completed") {
      await Promise.all([
        DbUtil.populateIfNotAlreadyPopulated(creditTicket, CreditTicketPopulationFieldsEnum.OWNER),
        DbUtil.populateIfNotAlreadyPopulated(creditTicket, CreditTicketPopulationFieldsEnum.PORTFOLIO)
      ]);

      const user = creditTicket.owner as UserDocument;
      const portfolio = creditTicket.portfolio as PortfolioDocument;

      await DbUtil.runInSession(async (session: mongoose.ClientSession) => {
        await PortfolioService.updateCashAvailability(
          portfolio.id,
          user.currency,
          Decimal.div(creditTicket.consideration.amount, 100).toNumber(),
          {
            available: true,
            settled: true,
            session
          }
        );

        await CreditTicket.findByIdAndUpdate(
          creditTicket.id,
          {
            "deposit.providers.wealthkernel.status": "Completed",
            status: "Credited",
            creditedAt: new Date(Date.now())
          },
          { session }
        );
      });
    } else {
      await CreditTicket.findByIdAndUpdate(creditTicket.id, {
        "deposit.providers.wealthkernel.status": newWkStatus
      });
    }
  }
}
