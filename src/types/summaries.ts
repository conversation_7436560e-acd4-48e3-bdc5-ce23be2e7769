import { investmentUniverseConfig, publicInvestmentUniverseConfig } from "@wealthyhood/shared-configs";

export type SentimentScoreWithLabelsType = {
  total: { score: number; label: SentimentLabelEnum };
  news?: { score: number; label: SentimentLabelEnum };
  analyst?: { score: number; label: SentimentLabelEnum };
  priceMomentum?: { score: number; label: SentimentLabelEnum };
};

export enum SentimentLabelEnum {
  OPTIMAL = "optimal",
  SUBOPTIMAL = "suboptimal",
  UNDERPERFORMING = "underperforming"
}

export type ReturnsType = { upBy: string } | { downBy: string };

export type TodayMarketsEntryType = { label: string; returns: ReturnsType };
export type TodayMarketsType = TodayMarketsEntryType[];

export type SavingsInterestDetailsType = {
  estimated?: boolean;
  unrealisedMonthlyInterest: string;
  dailyInterest: string;
} | null;

type SundownDigestType = string;

export type InvestmentsWithPerformanceType = InvestmentWithPerformanceType[];
type InvestmentWithPerformanceType = {
  assetId: investmentUniverseConfig.AssetType;
  value: string;
  weight: string;
} & ReturnsType;

export type MarketSummaryType = {
  overview?: string;
  sections?: {
    title?: string; // Concatenation of asset name and ticker
    tickerSymbol?: string; // Stored section.ticker
    assetReturns?: ReturnsType; // Only if assetId exists
    content?: string; // Stored section.content
    assetId?: publicInvestmentUniverseConfig.PublicAssetType;
    tag?: string; // Section category tag when no ticker is available
  }[];
};

type SnapshotPortfolioComponentType = {
  key: string;
  chartValue?: number; // Used for drawing the chart - this contains a logarithmic transformed value and not the actual value.
  displayValue: string;
  value: number;
};

export type DailySummaryPortfolioType = {
  cash: SnapshotPortfolioComponentType;
  savings: SnapshotPortfolioComponentType & SavingsInterestDetailsType;
  total: SnapshotPortfolioComponentType;
  holdings: SnapshotPortfolioComponentType & ReturnsType;
};

export type DailySummaryType = DailySummaryWithDataType | ChartLabelOnlyType;

export type DailySummaryWithDataType = {
  timestamp: number;
  chartLabel: string;
  shortDateLabel: string;
  fullDateLabel: string;
  portfolio: DailySummaryPortfolioType;
  sentimentScore?: SentimentScoreWithLabelsType;
  todayMarkets?: TodayMarketsType;
  topPerformers?: InvestmentsWithPerformanceType;
  performers: PerformersType;
  sundownDigest?: SundownDigestType;
  marketSummary?: MarketSummaryType;
  isUninvested?: boolean;
  seeAllTopPerformersEnabled?: boolean;
};

export type PerformersType = {
  all: InvestmentsWithPerformanceType;
  best: InvestmentsWithPerformanceType;
  worst: InvestmentsWithPerformanceType;
};

export type ChartLabelOnlyType = {
  timestamp: number;
  chartLabel: string;
  isOnlyForChartLabel: boolean;
};

export type DailySummariesType = {
  data: DailySummaryType[];
  maxValueDifferences?: Record<keyof DailySummaryPortfolioType, number>;
};
