import express from "express";
import ErrorMiddleware from "../middlewares/errorMiddleware";
import OrderController from "../controllers/orderController";
import OrderMiddleware from "../middlewares/orderMiddleware";

const router = express.Router();

/**
 * GET REQUESTS
 */
router.get("/pending", ErrorMiddleware.catchAsyncErrors(OrderController.getPendingOrders));
router.get("/matched/latest", ErrorMiddleware.catchAsyncErrors(OrderController.getLatestMatchedOrderId));

/**
 * POST REQUESTS
 */
router.post(
  "/:id/cancel",
  ErrorMiddleware.catchAsyncErrors(OrderMiddleware.loadOrder({ transaction: true })),
  ErrorMiddleware.catchAsyncErrors(OrderMiddleware.orderBelongsToUser),
  ErrorMiddleware.catchAsyncErrors(OrderController.cancelOrder)
);

export default router;
