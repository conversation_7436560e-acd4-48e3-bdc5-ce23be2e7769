import type { Readable } from "stream";
import { addBreadcrumb, captureException } from "@sentry/node";
import axios from "axios";
import logger from "./loggerService";
import { InternalServerError } from "../models/ApiErrors";
import { cloudflareConfig } from "@wealthyhood/shared-configs";
import { HeadObjectCommand, S3Client, S3ServiceException } from "@aws-sdk/client-s3";
import { Upload } from "@aws-sdk/lib-storage";
import { PartialRecord } from "utils";

/**
 * ENUMS
 */
enum HttpMethodEnum {
  GET = "GET",
  POST = "POST",
  PUT = "PUT"
}

export enum FilesEnum {
  POPULAR_ASSETS_OVERRIDE = "POPULAR_ASSETS_OVERRIDE"
}

export enum BucketsEnum {
  ACCOUNT_STATEMENTS = "account-statements",
  ETF_HOLDING_LOGOS = "etf-holdings-logos",
  POPULAR_ASSETS = "server-config",
  JUMIO_DATA = "jumio-data",
  HEAP_DUMPS = "heap-dumps"
}

export enum ContentTypeEnum {
  IMAGE_PNG = "image/png",
  APPLICATION_PDF = "application/pdf",
  APPLICATION_JSON = "application/json"
}

/**
 * CONFIG
 */
const CLOUDFLARE_API_URL = "https://api.cloudflare.com/client/v4";

export const IMAGES_URL = "https://images.wealthyhood.cloud";

export const BUCKET_URL_CONFIG: PartialRecord<BucketsEnum, string> = {
  [BucketsEnum.ACCOUNT_STATEMENTS]: "https://statements.wealthyhood.cloud",
  [BucketsEnum.POPULAR_ASSETS]: "https://pub-b466be118e5b4ea19a3eebd3c7c6bad7.r2.dev",
  [BucketsEnum.ETF_HOLDING_LOGOS]: "https://etf-holdings-logos.wealthyhood.dev"
};

type FileConfig = Record<FilesEnum, string>;
const FILE_CONFIG: Record<"development" | "staging" | "production", FileConfig> = {
  development: {
    [FilesEnum.POPULAR_ASSETS_OVERRIDE]: "/dev/popularAssets.json"
  },
  staging: {
    [FilesEnum.POPULAR_ASSETS_OVERRIDE]: "/staging/popularAssets.json"
  },
  production: {
    [FilesEnum.POPULAR_ASSETS_OVERRIDE]: "/popularAssets.json"
  }
};

const CLOUDFLARE_S3_API = "https://650a68f7d2bf978441462cc8f14f862c.r2.cloudflarestorage.com";

export default class CloudflareService {
  private static _accessToken?: string = process.env.CLOUDFLARE_API_TOKEN;
  private static _accountId?: string = process.env.CLOUDFLARE_ACCOUNT_ID;
  private static _kvNamespaceId?: string = process.env.CLOUDFLARE_KV_NAMESPACE_ID;
  private static _instance: CloudflareService;
  private _s3Client: S3Client;

  constructor() {
    const s3Credentials = CloudflareService._verifyS3Credentials();
    this._s3Client = new S3Client({
      endpoint: CLOUDFLARE_S3_API,
      region: "auto",
      credentials: {
        accessKeyId: s3Credentials.accessKey,
        secretAccessKey: s3Credentials.secret
      },
      forcePathStyle: true // Required for Cloudflare R2
    });
  }

  public static get Instance(): CloudflareService {
    return CloudflareService._instance || (CloudflareService._instance = new CloudflareService());
  }

  // ====================
  // KV NAMESPACES
  // ====================
  public async getDataFromFile<T>(file: FilesEnum, bucket: BucketsEnum): Promise<T> {
    const fileUrl = CloudflareService._getFileUrl(file, bucket);

    return await CloudflareService._fetch({ method: HttpMethodEnum.GET, url: fileUrl });
  }

  public async updateKeyValuePair(kvNamespceKey: cloudflareConfig.KvNamespaceKeys, data: string): Promise<void> {
    await CloudflareService._fetch({
      method: HttpMethodEnum.PUT,
      url: `${CLOUDFLARE_API_URL}/accounts/${CloudflareService._accountId}/storage/kv/namespaces/${CloudflareService._kvNamespaceId}/values/${kvNamespceKey}`,
      data
    });
  }

  // ====================
  // R2 BUCKETS
  // ====================
  public async uploadObject(
    bucket: BucketsEnum,
    objectKey: string,
    objectData: Readable,
    options: { contentType: ContentTypeEnum }
  ): Promise<{ fileUri: string }> {
    const params = {
      Bucket: bucket.toString(),
      Key: objectKey,
      Body: objectData,
      ContentType: options.contentType.toString()
    };

    const uploader = new Upload({
      client: this._s3Client,
      params
    });
    await uploader.done();

    return { fileUri: `${BUCKET_URL_CONFIG[bucket]}/${objectKey}` };
  }

  public async doesObjectExists(bucket: BucketsEnum, objectKey: string): Promise<boolean> {
    const command = new HeadObjectCommand({
      Bucket: bucket.toString(),
      Key: objectKey
    });

    try {
      await this._s3Client.send(command);

      return true;
    } catch (error) {
      if ((error as S3ServiceException).$metadata?.httpStatusCode === 404) {
        return false;
      }

      throw error;
    }
  }

  public getResizedImageURL(imageURL: string, options: { width: number; height: number }): string {
    return `${IMAGES_URL}/cdn-cgi/image/width=${options.width},height=${options.height},fit=cover/${imageURL}`;
  }

  /**
   * PRIVATE METHODS
   */
  private static _verifyS3Credentials(): { accessKey: string; secret: string } {
    const accessKey = process.env.CLOUDFLARE_S3_ACCESS_KEY;
    const secret = process.env.CLOUDFLARE_S3_SECRET;
    if (!accessKey || !secret) {
      throw new Error("Cloudflare S3 secret are missing");
    }
    return { accessKey, secret };
  }

  private static _getFileUrl(file: FilesEnum, bucket: BucketsEnum): string {
    const fileEndpoint = FILE_CONFIG[process.env.NODE_ENV as "development" | "staging" | "production"][file];
    if (!fileEndpoint) {
      throw new InternalServerError("Could not find file url.");
    }

    return BUCKET_URL_CONFIG[bucket] + fileEndpoint;
  }

  /**
   * @description This is the method for making any requests to access the Coudflare API, other than authentication.
   * It is configured to have the Bearer token in the header of the request.
   * @param method get or post
   * @param url the Cloudflare endpoint that we want to access as defined in the endpoint enum
   * @param data any data that may be posted with the request
   */
  private static async _fetch({
    method,
    url,
    data
  }: {
    method: HttpMethodEnum;
    url: string;
    data?: any;
  }): Promise<any> {
    try {
      const accessToken = CloudflareService._accessToken;
      if (!accessToken) {
        throw new InternalServerError("Cloudflare token is missing");
      }

      const response = await axios({
        method,
        url,
        headers: {
          Authorization: `Bearer ${accessToken}`
        },
        data
      });
      return response.data;
    } catch (err) {
      logger.error("http request failed", {
        module: "CloudflareService",
        method: "_fetch",
        data: {
          url,
          method: method.toUpperCase(),
          // eslint-disable-next-line camelcase
          status: err.response && err.response.status,
          reason: err.response ? JSON.stringify(err.response.data, null, 4) : "",
          requestData: {
            data: JSON.stringify(data, null, 4)
          },
          error: err
        }
      });
      addBreadcrumb({
        type: "http",
        category: "http",
        level: "error",
        data: {
          url,
          method: method.toUpperCase(),
          // eslint-disable-next-line camelcase
          status_code: err.response && err.response.status,
          reason: err.response && JSON.stringify(err.response.data, null, 4),
          requestData: {
            data: JSON.stringify(data, null, 4)
          }
        }
      });
      captureException(err);
    }
  }
}
