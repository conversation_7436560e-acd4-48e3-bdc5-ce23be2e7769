/* eslint-disable no-undef */
jest.mock("../loaders/eventEmitter");
jest.mock("../loaders/redis");
jest.mock("../external-services/loggerService");
jest.mock("../external-services/configCatService");
jest.mock("../middlewares/authMiddleware");
jest.mock("../external-services/mailerService");
jest.mock("../external-services/contentfulRetrievalService");
jest.mock("../external-services/contentfulManagementService");
jest.mock("../external-services/finimizeService");
jest.mock("../external-services/stripeService");
jest.mock("../external-services/cloudflareService");
jest.mock("../external-services/complyAdvantageService");
jest.mock("../external-services/eodService");
jest.mock("../external-services/auth0ManagementService");
jest.mock("../external-services/goCardlessPaymentsService");

// Socket.io
jest.mock("../socket/emitter");

// AI lib mocks
jest.mock("../lib/marketSummaryFormatter");
jest.mock("../lib/tickerFinder");
jest.mock("../lib/assetIdResolver");
jest.mock("../lib/learnNewsCleaner");

jest.mock("auth0", () => {
  // Require the original module to not be mocked...
  const originalModule = jest.requireActual("auth0");

  return {
    ...originalModule,
    ManagementClient: jest
      .fn()
      .mockImplementation(() => ({ users: { get: jest.fn(), delete: jest.fn(), link: jest.fn() } }))
  };
});

jest.mock("axios", () => {
  // Require the original module to not be mocked...
  const originalModule = jest.requireActual("axios");

  return {
    ...originalModule,
    get: jest.fn(),
    post: jest.fn().mockReturnValue({ data: { access_token: 123, expires_in: 600000 } })
  };
});

jest.mock("@sentry/node", () => ({
  init: jest.fn(),
  Integrations: {
    Http: jest.fn()
  },
  extraErrorDataIntegration: jest.fn(),
  httpIntegration: jest.fn(() => ({
    ignoreOutgoingRequests: jest.fn()
  })),
  expressIntegration: jest.fn(),
  mongoIntegration: jest.fn(),
  mongooseIntegration: jest.fn(),
  setupExpressErrorHandler: jest.fn(),
  setUser: jest.fn(),
  captureException: jest.fn(),
  captureMessage: jest.fn(),
  addBreadcrumb: jest.fn()
}));

jest.mock("@sentry/profiling-node", () => ({
  nodeProfilingIntegration: jest.fn()
}));
