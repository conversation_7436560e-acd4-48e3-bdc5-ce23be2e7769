import { CronJobNameEnum } from "./cronNames";
import DateUtil from "../../utils/dateUtil";

const PRODUCTION_CRON_CONFIG: {
  name: CronJobNameEnum;
  cron: string;
  closeWorkerAfterMs?: number;
  lockExpirationSec?: number;
  memoryMonitoringSampleRate?: number;
}[] = [
  { name: CronJobNameEnum.ASSET_DISCOVERY_DATA, cron: "5/20 8-21 * * *" },
  { name: CronJobNameEnum.ASSET_NEWS, cron: "40 6,15 * * 1-5" },
  { name: CronJobNameEnum.ASSET_FUNDAMENTALS_DATA, cron: "0 2 * * 1-6" },
  { name: Cron<PERSON>obNameEnum.ASSET_HISTORICAL_PRICES, cron: "0 1 * * 1-6" },
  { name: CronJobNameEnum.AUTOMATED_REBALANCES, cron: "0 2-5 1-10 * *" },
  { name: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>.AUTOMATED_INVESTMENTS, cron: "0/10 14 * * 1-5" },
  { name: CronJobNameEnum.CACHE_FX_RATES, cron: "6/20 * * * *" },
  { name: CronJobNameEnum.CHARGE_CREATION, cron: "0 3 * * 1-6" },
  // We submit charges after the 3rd day of the month, to allow cash to settle before the charges are sent.
  { name: CronJobNameEnum.CHARGE_SUBMISSION, cron: "0,20,40 12 3-5 * *" },
  { name: CronJobNameEnum.CUSTODY_CHARGES_CREATION, cron: "*/10 8 24-31 * *" },
  // Run the daily recap creation cron job at 5:17 AM UTC Tuesday to Saturday (7:17 or 8:17 AM Greek time, depending on DST)
  // Creates notifications that will be sent later at 9:00 AM Greek time
  { name: CronJobNameEnum.DAILY_RECAP_CREATION, cron: "17 5-6 * * 2-6" },
  { name: CronJobNameEnum.DOWNGRADE_SUBSCRIPTIONS, cron: "5/30 22-23 * * *" },
  {
    name: CronJobNameEnum.INTRA_DAY_ASSET_TICKER,
    cron: "3/5 8-22 * * 1-5",
    closeWorkerAfterMs: DateUtil.convertMinutesToMilliseconds(2),
    lockExpirationSec: DateUtil.convertMinutesToSeconds(12),
    memoryMonitoringSampleRate: 0.05 // Monitor memory for 5% of runs
  },
  {
    name: CronJobNameEnum.STALE_ASSET_TICKER,
    cron: "45 12-21 * * 1-5"
  },
  {
    name: CronJobNameEnum.ONGOING,
    cron: "1/5 * * * *",
    closeWorkerAfterMs: DateUtil.convertMinutesToMilliseconds(4),
    lockExpirationSec: DateUtil.convertMinutesToSeconds(12),
    memoryMonitoringSampleRate: 0.05
  },
  { name: CronJobNameEnum.ORDER_SUBMISSION, cron: "*/10 7-21 * * 1-5" },
  { name: CronJobNameEnum.SAVINGS_DIVIDENDS, cron: "*/20 8 1-10 * *" },
  { name: CronJobNameEnum.DAILY_SAVINGS_PRODUCT_TICKER, cron: "2/10 22 * * 1-5" },
  { name: CronJobNameEnum.DAILY_PORTFOLIO_SAVINGS_TICKER, cron: "34/10 22 * * *" }, // this depends on DAILY_SAVINGS_PRODUCT_TICKER
  { name: CronJobNameEnum.SAVINGS_PRODUCT_DATA_UPDATE_CHECK, cron: "*/10 19-23 * * *" },
  { name: CronJobNameEnum.STRIPE_PAYMENT_REJECTION, cron: "15/30 21-22 * * *" },
  { name: CronJobNameEnum.WK_CASH_HOLDINGS_MISMATCH, cron: "45 9-10 * * 2-6" },
  { name: CronJobNameEnum.CACHE_SENTIMENT_SCORES, cron: "0 */2 * * *" },
  { name: CronJobNameEnum.CACHE_PORTFOLIO_MWRR_AND_UP_BY_VALUES, cron: "25 0-2 * * *" },
  { name: CronJobNameEnum.ETF_HOLDINGS_LOGOS, cron: "*/20 7 * * 6" },
  { name: CronJobNameEnum.SUNDOWN_DIGEST, cron: "45 0-2 * * *" },
  { name: CronJobNameEnum.CACHE_INDEX_PRICES, cron: "8/5 * * * 1-5" },
  { name: CronJobNameEnum.STORE_INDEX_PRICES, cron: "15/15 22 * * 1-5" },
  { name: CronJobNameEnum.GIFTS, cron: "3 8-23/2 * * *" },
  { name: CronJobNameEnum.GIFT_CAPABILITIES, cron: "3 8-23/2 * * *" },
  { name: CronJobNameEnum.INVESTMENT_DIVIDENDS, cron: "35 9-10 * * 1-5" },
  {
    name: CronJobNameEnum.INTRA_DAY_PORTFOLIO_TICKER,
    cron: "8/10 7-22 * * 1-5",
    closeWorkerAfterMs: DateUtil.convertMinutesToMilliseconds(6),
    lockExpirationSec: DateUtil.convertMinutesToSeconds(6),
    memoryMonitoringSampleRate: 0.05
  },
  {
    name: CronJobNameEnum.DAILY_SUMMARY_SNAPSHOT,
    cron: "5/25 23 * * 1-5",
    lockExpirationSec: DateUtil.convertMinutesToSeconds(20)
  },
  {
    name: CronJobNameEnum.NIGHTLY,
    cron: "*/90 20-23 * * *",
    closeWorkerAfterMs: DateUtil.convertMinutesToMilliseconds(60),
    lockExpirationSec: DateUtil.convertMinutesToSeconds(60)
  },
  { name: CronJobNameEnum.STOCK_SPLIT, cron: "*/20 8-9 * * *" },
  { name: CronJobNameEnum.PORTFOLIO_TICKER, cron: "10/15 22 * * 1-5" },
  {
    name: CronJobNameEnum.PUBLIC_ASSET_DATA,
    cron: "0 3 * * 1-6",
    closeWorkerAfterMs: DateUtil.convertMinutesToMilliseconds(60),
    lockExpirationSec: DateUtil.convertMinutesToSeconds(60)
  },
  { name: CronJobNameEnum.REWARDS, cron: "*/10 7 * * 1-5", memoryMonitoringSampleRate: 0.05 },
  { name: CronJobNameEnum.RISK_ASSESSMENT, cron: "0 1 * * *" },
  { name: CronJobNameEnum.SAVINGS_PRODUCTS_DATA, cron: "*/20 2 * * *" },
  { name: CronJobNameEnum.SYNC_TRANSACTIONS_WITH_ORDERS, cron: "4/15 7-20 * * 1-5" },
  { name: CronJobNameEnum.TRANSACTION_MONITOR, cron: "35 12,15,18 * * *" },
  { name: CronJobNameEnum.USER_DATA_REQUESTS, cron: "50 */2 * * *" },
  { name: CronJobNameEnum.WH_DIVIDEND_BONUS, cron: "45 */1 * * *" },
  { name: CronJobNameEnum.WH_DIVIDEND_CREATION, cron: "*/10 8 24-31 * *", memoryMonitoringSampleRate: 0.05 },
  { name: CronJobNameEnum.CONTENT_INGESTION, cron: "40 2-22 * * *" },
  // The notification cron job is run at 15 minutes past the hour, to have enough time to send out
  // notifications for content entries that are published as specified by publishAt.
  { name: CronJobNameEnum.NOTIFICATION, cron: "15 * * * *" },
  { name: CronJobNameEnum.LEARN_NEWS, cron: "39 */3 * * *" },
  // Wealthybites - Saturday
  { name: CronJobNameEnum.CACHE_ASSETS_WEEKLY_RETURNS, cron: "8 1 * * 6" }, // Saturday 01:08 UTC
  { name: CronJobNameEnum.WEALTHYBITES_DATA_PREP, cron: "30 3 * * 6" }, // Saturday 03:30 UTC
  { name: CronJobNameEnum.WEALTHYBITES_EMAIL_SEND, cron: "0 9 * * 6" } // Saturday 09:00 UTC
  // { name: CronJobNameEnum.MONGO_DUMP, cron: "0 6 * * 1-5" }
];

export default PRODUCTION_CRON_CONFIG;
